"use strict";var he=Object.create;var W=Object.defineProperty;var Ie=Object.getOwnPropertyDescriptor;var Ue=Object.getOwnPropertyNames;var be=Object.getPrototypeOf,Ee=Object.prototype.hasOwnProperty;var ke=(e,s)=>{for(var f in s)W(e,f,{get:s[f],enumerable:!0})},j=(e,s,f,g)=>{if(s&&typeof s=="object"||typeof s=="function")for(let P of Ue(s))!Ee.call(e,P)&&P!==f&&W(e,P,{get:()=>s[P],enumerable:!(g=Ie(s,P))||g.enumerable});return e},k=(e,s,f)=>(j(e,s,"default"),f&&j(f,s,"default")),Me=(e,s,f)=>(f=e!=null?he(be(e)):{},j(s||!e||!e.__esModule?W(f,"default",{value:e,enumerable:!0}):f,e)),Oe=e=>j(W({},"__esModule",{value:!0}),e);var U={};ke(U,{ApiProvider:()=>Be,UNINITIALIZED_VALUE:()=>H,createApi:()=>He,reactHooksModule:()=>Qe,reactHooksModuleName:()=>fe});module.exports=Oe(U);var te=require("@reduxjs/toolkit/query");var et=require("@reduxjs/toolkit"),M=require("react-redux"),De=require("reselect");function V(e){return e.replace(e[0],e[0].toUpperCase())}function de(e){return e.type==="query"}function ce(e){return e.type==="mutation"}function $(e){return e.type==="infinitequery"}function C(e,...s){return Object.assign(e,...s)}var ge=require("@reduxjs/toolkit"),A=require("@reduxjs/toolkit/query"),n=require("react"),Y=require("react-redux");var H=Symbol();var z=require("react"),le=require("@reduxjs/toolkit/query");function G(e){let s=(0,z.useRef)(e),f=(0,z.useMemo)(()=>(0,le.copyWithStructuralSharing)(s.current,e),[e]);return(0,z.useEffect)(()=>{s.current!==f&&(s.current=f)},[f]),f}var Z=require("react"),ye=require("react-redux");function _(e){let s=(0,Z.useRef)(e);return(0,Z.useEffect)(()=>{(0,ye.shallowEqual)(s.current,e)||(s.current=e)},[e]),(0,ye.shallowEqual)(s.current,e)?s.current:e}var Fe=()=>typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",we=Fe(),ve=()=>typeof navigator<"u"&&navigator.product==="ReactNative",Le=ve(),Ne=()=>we||Le?n.useLayoutEffect:n.useEffect,Ce=Ne(),Re=e=>e.isUninitialized?{...e,isUninitialized:!1,isFetching:!0,isLoading:e.data===void 0,status:A.QueryStatus.pending}:e;function oe(e,...s){let f={};return s.forEach(g=>{f[g]=e[g]}),f}var pe=["data","status","isLoading","isSuccess","isError","error"];function Te({api:e,moduleOptions:{batch:s,hooks:{useDispatch:f,useSelector:g,useStore:P},unstable__sideEffectsInRender:h,createSelector:L},serializeQueryArgs:b,context:E}){let O=h?t=>t():n.useEffect;return{buildQueryHooks:q,buildInfiniteQueryHooks:se,buildMutationHook:ae,usePrefetch:ie};function ne(t,a,d){if(a?.endpointName&&t.isUninitialized){let{endpointName:y}=a,p=E.endpointDefinitions[y];d!==A.skipToken&&b({queryArgs:a.originalArgs,endpointDefinition:p,endpointName:y})===b({queryArgs:d,endpointDefinition:p,endpointName:y})&&(a=void 0)}let o=t.isSuccess?t.data:a?.data;o===void 0&&(o=t.data);let u=o!==void 0,r=t.isLoading,i=(!a||a.isLoading||a.isUninitialized)&&!u&&r,Q=t.isSuccess||u&&(r&&!a?.isError||t.isUninitialized);return{...t,data:o,currentData:t.data,isFetching:r,isLoading:i,isSuccess:Q}}function re(t,a,d){if(a?.endpointName&&t.isUninitialized){let{endpointName:y}=a,p=E.endpointDefinitions[y];d!==A.skipToken&&b({queryArgs:a.originalArgs,endpointDefinition:p,endpointName:y})===b({queryArgs:d,endpointDefinition:p,endpointName:y})&&(a=void 0)}let o=t.isSuccess?t.data:a?.data;o===void 0&&(o=t.data);let u=o!==void 0,r=t.isLoading,i=(!a||a.isLoading||a.isUninitialized)&&!u&&r,Q=t.isSuccess||r&&u;return{...t,data:o,currentData:t.data,isFetching:r,isLoading:i,isSuccess:Q}}function ie(t,a){let d=f(),o=_(a);return(0,n.useCallback)((u,r)=>d(e.util.prefetch(t,u,{...o,...r})),[t,d,o])}function m(t,a,{refetchOnReconnect:d,refetchOnFocus:o,refetchOnMountOrArgChange:u,skip:r=!1,pollingInterval:i=0,skipPollingIfUnfocused:Q=!1,...y}={}){let{initiate:p}=e.endpoints[t],l=f(),S=(0,n.useRef)(void 0);if(!S.current){let v=l(e.internalActions.internal_getRTKQSubscriptions());S.current=v}let c=G(r?A.skipToken:a),R=_({refetchOnReconnect:d,refetchOnFocus:o,pollingInterval:i,skipPollingIfUnfocused:Q}),T=y.initialPageParam,D=_(T),x=(0,n.useRef)(void 0),{queryCacheKey:I,requestId:w}=x.current||{},K=!1;I&&w&&(K=S.current.isRequestSubscribed(I,w));let ue=!K&&x.current!==void 0;return O(()=>{ue&&(x.current=void 0)},[ue]),O(()=>{let v=x.current;if(typeof process<"u",c===A.skipToken){v?.unsubscribe(),x.current=void 0;return}let Ae=x.current?.subscriptionOptions;if(!v||v.arg!==c){v?.unsubscribe();let Pe=l(p(c,{subscriptionOptions:R,forceRefetch:u,...$(E.endpointDefinitions[t])?{initialPageParam:D}:{}}));x.current=Pe}else R!==Ae&&v.updateSubscriptionOptions(R)},[l,p,u,c,R,ue,D,t]),[x,l,p,R]}function N(t,a){return(o,{skip:u=!1,selectFromResult:r}={})=>{let{select:i}=e.endpoints[t],Q=G(u?A.skipToken:o),y=(0,n.useRef)(void 0),p=(0,n.useMemo)(()=>L([i(Q),(T,D)=>D,T=>Q],a,{memoizeOptions:{resultEqualityCheck:Y.shallowEqual}}),[i,Q]),l=(0,n.useMemo)(()=>r?L([p],r,{devModeChecks:{identityFunctionCheck:"never"}}):p,[p,r]),S=g(T=>l(T,y.current),Y.shallowEqual),c=P(),R=p(c.getState(),y.current);return Ce(()=>{y.current=R},[R]),S}}function B(t){(0,n.useEffect)(()=>()=>{t.current?.unsubscribe?.(),t.current=void 0},[t])}function F(t){if(!t.current)throw new Error((0,ge.formatProdErrorMessage)(38));return t.current.refetch()}function q(t){let a=(u,r={})=>{let[i]=m(t,u,r);return B(i),(0,n.useMemo)(()=>({refetch:()=>F(i)}),[i])},d=({refetchOnReconnect:u,refetchOnFocus:r,pollingInterval:i=0,skipPollingIfUnfocused:Q=!1}={})=>{let{initiate:y}=e.endpoints[t],p=f(),[l,S]=(0,n.useState)(H),c=(0,n.useRef)(void 0),R=_({refetchOnReconnect:u,refetchOnFocus:r,pollingInterval:i,skipPollingIfUnfocused:Q});O(()=>{let I=c.current?.subscriptionOptions;R!==I&&c.current?.updateSubscriptionOptions(R)},[R]);let T=(0,n.useRef)(R);O(()=>{T.current=R},[R]);let D=(0,n.useCallback)(function(I,w=!1){let K;return s(()=>{c.current?.unsubscribe(),c.current=K=p(y(I,{subscriptionOptions:T.current,forceRefetch:!w})),S(I)}),K},[p,y]),x=(0,n.useCallback)(()=>{c.current?.queryCacheKey&&p(e.internalActions.removeQueryResult({queryCacheKey:c.current?.queryCacheKey}))},[p]);return(0,n.useEffect)(()=>()=>{c?.current?.unsubscribe()},[]),(0,n.useEffect)(()=>{l!==H&&!c.current&&D(l,!0)},[l,D]),(0,n.useMemo)(()=>[D,l,{reset:x}],[D,l,x])},o=N(t,ne);return{useQueryState:o,useQuerySubscription:a,useLazyQuerySubscription:d,useLazyQuery(u){let[r,i,{reset:Q}]=d(u),y=o(i,{...u,skip:i===H}),p=(0,n.useMemo)(()=>({lastArg:i}),[i]);return(0,n.useMemo)(()=>[r,{...y,reset:Q},p],[r,y,Q,p])},useQuery(u,r){let i=a(u,r),Q=o(u,{selectFromResult:u===A.skipToken||r?.skip?void 0:Re,...r}),y=oe(Q,...pe);return(0,n.useDebugValue)(y),(0,n.useMemo)(()=>({...Q,...i}),[Q,i])}}}function se(t){let a=(o,u={})=>{let[r,i,Q,y]=m(t,o,u),p=(0,n.useRef)(y);O(()=>{p.current=y},[y]);let l=(0,n.useCallback)(function(R,T){let D;return s(()=>{r.current?.unsubscribe(),r.current=D=i(Q(R,{subscriptionOptions:p.current,direction:T}))}),D},[r,i,Q]);B(r);let S=G(u.skip?A.skipToken:o),c=(0,n.useCallback)(()=>F(r),[r]);return(0,n.useMemo)(()=>({trigger:l,refetch:c,fetchNextPage:()=>l(S,"forward"),fetchPreviousPage:()=>l(S,"backward")}),[c,l,S])},d=N(t,re);return{useInfiniteQueryState:d,useInfiniteQuerySubscription:a,useInfiniteQuery(o,u){let{refetch:r,fetchNextPage:i,fetchPreviousPage:Q}=a(o,u),y=d(o,{selectFromResult:o===A.skipToken||u?.skip?void 0:Re,...u}),p=oe(y,...pe,"hasNextPage","hasPreviousPage");return(0,n.useDebugValue)(p),(0,n.useMemo)(()=>({...y,fetchNextPage:i,fetchPreviousPage:Q,refetch:r}),[y,i,Q,r])}}}function ae(t){return({selectFromResult:a,fixedCacheKey:d}={})=>{let{select:o,initiate:u}=e.endpoints[t],r=f(),[i,Q]=(0,n.useState)();(0,n.useEffect)(()=>()=>{i?.arg.fixedCacheKey||i?.reset()},[i]);let y=(0,n.useCallback)(function(I){let w=r(u(I,{fixedCacheKey:d}));return Q(w),w},[r,u,d]),{requestId:p}=i||{},l=(0,n.useMemo)(()=>o({fixedCacheKey:d,requestId:i?.requestId}),[d,i,o]),S=(0,n.useMemo)(()=>a?L([l],a):l,[a,l]),c=g(S,Y.shallowEqual),R=d==null?i?.arg.originalArgs:void 0,T=(0,n.useCallback)(()=>{s(()=>{i&&Q(void 0),d&&r(e.internalActions.removeMutationResult({requestId:p,fixedCacheKey:d}))})},[r,d,i,p]),D=oe(c,...pe,"endpointName");(0,n.useDebugValue)(D);let x=(0,n.useMemo)(()=>({...c,originalArgs:R,reset:T}),[c,R,T]);return(0,n.useMemo)(()=>[y,x],[y,x])}}}var fe=Symbol(),Qe=({batch:e=M.batch,hooks:s={useDispatch:M.useDispatch,useSelector:M.useSelector,useStore:M.useStore},createSelector:f=De.createSelector,unstable__sideEffectsInRender:g=!1,...P}={})=>({name:fe,init(h,{serializeQueryArgs:L},b){let E=h,{buildQueryHooks:O,buildInfiniteQueryHooks:ne,buildMutationHook:re,usePrefetch:ie}=Te({api:h,moduleOptions:{batch:e,hooks:s,unstable__sideEffectsInRender:g,createSelector:f},serializeQueryArgs:L,context:b});return C(E,{usePrefetch:ie}),C(b,{batch:e}),{injectEndpoint(m,N){if(de(N)){let{useQuery:B,useLazyQuery:F,useLazyQuerySubscription:q,useQueryState:se,useQuerySubscription:ae}=O(m);C(E.endpoints[m],{useQuery:B,useLazyQuery:F,useLazyQuerySubscription:q,useQueryState:se,useQuerySubscription:ae}),h[`use${V(m)}Query`]=B,h[`useLazy${V(m)}Query`]=F}if(ce(N)){let B=re(m);C(E.endpoints[m],{useMutation:B}),h[`use${V(m)}Mutation`]=B}else if($(N)){let{useInfiniteQuery:B,useInfiniteQuerySubscription:F,useInfiniteQueryState:q}=ne(m);C(E.endpoints[m],{useInfiniteQuery:B,useInfiniteQuerySubscription:F,useInfiniteQueryState:q}),h[`use${V(m)}InfiniteQuery`]=B}}}}});k(U,require("@reduxjs/toolkit/query"),module.exports);var J=require("@reduxjs/toolkit"),xe=require("react"),me=require("react"),X=Me(require("react")),ee=require("react-redux"),Se=require("@reduxjs/toolkit/query");function Be(e){let s=e.context||ee.ReactReduxContext;if((0,xe.useContext)(s))throw new Error((0,J.formatProdErrorMessage)(35));let[g]=X.useState(()=>(0,J.configureStore)({reducer:{[e.api.reducerPath]:e.api.reducer},middleware:P=>P().concat(e.api.middleware)}));return(0,me.useEffect)(()=>e.setupListeners===!1?void 0:(0,Se.setupListeners)(g.dispatch,e.setupListeners),[e.setupListeners,g.dispatch]),X.createElement(ee.Provider,{store:g,context:s},e.children)}var He=(0,te.buildCreateApi)((0,te.coreModule)(),Qe());0&&(module.exports={ApiProvider,UNINITIALIZED_VALUE,createApi,reactHooksModule,reactHooksModuleName,...require("@reduxjs/toolkit/query")});
//# sourceMappingURL=rtk-query-react.production.min.cjs.map