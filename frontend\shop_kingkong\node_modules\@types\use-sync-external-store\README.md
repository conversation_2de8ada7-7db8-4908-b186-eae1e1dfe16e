# Installation
> `npm install --save @types/use-sync-external-store`

# Summary
This package contains type definitions for use-sync-external-store (https://github.com/facebook/react#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/use-sync-external-store.

### Additional Details
 * Last updated: Tue, 07 Nov 2023 15:11:36 GMT
 * Dependencies: none

# Credits
These definitions were written by [eps1lon](https://github.com/eps1lon), and [<PERSON>](https://github.com/marker<PERSON>son).
