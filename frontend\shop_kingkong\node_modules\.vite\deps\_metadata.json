{"hash": "40b30c34", "configHash": "99dceb83", "lockfileHash": "4590248d", "browserHash": "fa249373", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "e8349535", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "1161f977", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "93202a2c", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "520cbfeb", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "be39ee65", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "df16a690", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "aa50c51c", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "2fdf3bbe", "needsInterop": false}, "react-toastify": {"src": "../../react-toastify/dist/index.mjs", "file": "react-toastify.js", "fileHash": "0be05644", "needsInterop": false}}, "chunks": {"chunk-AUFLGUIX": {"file": "chunk-AUFLGUIX.js"}, "chunk-PSQR3SVX": {"file": "chunk-PSQR3SVX.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}