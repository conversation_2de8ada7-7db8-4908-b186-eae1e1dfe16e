export*from"redux";import{produce as vo,current as Do,freeze as Oo,original as No,isDraft as jo}from"immer";import{createSelector as Vo,createSelectorCreator as _o,lruMemoize as Lo,weakMapMemoize as Uo}from"reselect";import{current as at,isDraft as it}from"immer";import{createSelectorCreator as st,weakMapMemoize as ct}from"reselect";var xe=(...e)=>{let t=st(...e),r=Object.assign((...n)=>{let o=t(...n),a=(s,...y)=>o(it(s)?at(s):s,...y);return Object.assign(a,o),a},{withTypes:()=>r});return r},ie=xe(ct);import{applyMiddleware as xt,createStore as Ct,compose as Et,combineReducers as Rt,isPlainObject as wt}from"redux";import{compose as Ce}from"redux";var Ee=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?Ce:Ce.apply(null,arguments)},Rn=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__?window.__REDUX_DEVTOOLS_EXTENSION__:function(){return function(e){return e}};import{thunk as mt,withExtraArgument as gt}from"redux-thunk";import{isAction as Re}from"redux";var q=e=>e&&typeof e.match=="function";function M(e,t){function r(...n){if(t){let o=t(...n);if(!o)throw new Error(x(0));return{type:e,payload:o.payload,..."meta"in o&&{meta:o.meta},..."error"in o&&{error:o.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=n=>Re(n)&&n.type===e,r}function se(e){return typeof e=="function"&&"type"in e&&q(e)}function ce(e){return Re(e)&&Object.keys(e).every(dt)}function dt(e){return["type","payload","error","meta"].indexOf(e)>-1}function ut(e){let t=e?`${e}`.split("/"):[],r=t[t.length-1]||"actionCreator";return`Detected an action creator with type "${e||"unknown"}" being dispatched. 
Make sure you're calling the action creator before dispatching, i.e. \`dispatch(${r}())\` instead of \`dispatch(${r})\`. This is necessary even if the action has no payload.`}function lt(e={}){return()=>r=>n=>r(n)}import{produce as pt,isDraftable as ft}from"immer";var F=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...t){return super.concat.apply(this,t)}prepend(...t){return t.length===1&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function de(e){return ft(e)?pt(e,()=>{}):e}function P(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}function yt(e){return typeof e!="object"||e==null||Object.isFrozen(e)}function ht(e={}){if(1)return()=>n=>o=>n(o);var t,r}import{isPlainObject as At}from"redux";function we(e){let t=typeof e;return e==null||t==="string"||t==="boolean"||t==="number"||Array.isArray(e)||At(e)}function Me(e,t="",r=we,n,o=[],a){let s;if(!r(e))return{keyPath:t||"<root>",value:e};if(typeof e!="object"||e===null||a?.has(e))return!1;let y=n!=null?n(e):Object.entries(e),c=o.length>0;for(let[l,i]of y){let d=t?t+"."+l:l;if(!(c&&o.some(g=>g instanceof RegExp?g.test(d):d===g))){if(!r(i))return{keyPath:d,value:i};if(typeof i=="object"&&(s=Me(i,d,r,n,o,a),s))return s}}return a&&Pe(e)&&a.add(e),!1}function Pe(e){if(!Object.isFrozen(e))return!1;for(let t of Object.values(e))if(!(typeof t!="object"||t===null)&&!Pe(t))return!1;return!0}function Tt(e={}){return()=>t=>r=>t(r)}function kt(e){return typeof e=="boolean"}var be=()=>function(t){let{thunk:r=!0,immutableCheck:n=!0,serializableCheck:o=!0,actionCreatorCheck:a=!0}=t??{},s=new F;return r&&(kt(r)?s.push(mt):s.push(gt(r.extraArgument))),s};var ue="RTK_autoBatch",St=()=>e=>({payload:e,meta:{[ue]:!0}}),Ie=e=>t=>{setTimeout(t,e)},le=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),o=!0,a=!1,s=!1,y=new Set,c=e.type==="tick"?queueMicrotask:e.type==="raf"?typeof window<"u"&&window.requestAnimationFrame?window.requestAnimationFrame:Ie(10):e.type==="callback"?e.queueNotification:Ie(e.timeout),l=()=>{s=!1,a&&(a=!1,y.forEach(i=>i()))};return Object.assign({},n,{subscribe(i){let d=()=>o&&i(),T=n.subscribe(d);return y.add(i),()=>{T(),y.delete(i)}},dispatch(i){try{return o=!i?.meta?.[ue],a=!o,a&&(s||(s=!0,c(l))),n.dispatch(i)}finally{o=!0}}})};var ve=e=>function(r){let{autoBatch:n=!0}=r??{},o=new F(e);return n&&o.push(le(typeof n=="object"?n:void 0)),o};function Mt(e){let t=be(),{reducer:r=void 0,middleware:n,devTools:o=!0,duplicateMiddlewareCheck:a=!0,preloadedState:s=void 0,enhancers:y=void 0}=e||{},c;if(typeof r=="function")c=r;else if(wt(r))c=Rt(r);else throw new Error(x(1));let l;typeof n=="function"?l=n(t):l=t();let i=Et;o&&(i=Ee({trace:!1,...typeof o=="object"&&o}));let d=xt(...l),T=ve(d),g=typeof y=="function"?y(T):T(),p=i(...g);return Ct(c,s,p)}import{produce as Pt,isDraft as bt,isDraftable as It}from"immer";function $(e){let t={},r=[],n,o={addCase(a,s){let y=typeof a=="string"?a:a.type;if(!y)throw new Error(x(28));if(y in t)throw new Error(x(29));return t[y]=s,o},addAsyncThunk(a,s){return s.pending&&(t[a.pending.type]=s.pending),s.rejected&&(t[a.rejected.type]=s.rejected),s.fulfilled&&(t[a.fulfilled.type]=s.fulfilled),s.settled&&r.push({matcher:a.settled,reducer:s.settled}),o},addMatcher(a,s){return r.push({matcher:a,reducer:s}),o},addDefaultCase(a){return n=a,o}};return e(o),[t,r,n]}function vt(e){return typeof e=="function"}function pe(e,t){let[r,n,o]=$(t),a;if(vt(e))a=()=>de(e());else{let y=de(e);a=()=>y}function s(y=a(),c){let l=[r[c.type],...n.filter(({matcher:i})=>i(c)).map(({reducer:i})=>i)];return l.filter(i=>!!i).length===0&&(l=[o]),l.reduce((i,d)=>{if(d)if(bt(i)){let g=d(i,c);return g===void 0?i:g}else{if(It(i))return Pt(i,T=>d(T,c));{let T=d(i,c);if(T===void 0){if(i===null)return i;throw Error("A case reducer on a non-draftable value must not return undefined")}return T}}return i},y)}return s.getInitialState=a,s}var De=(e,t)=>q(e)?e.match(t):e(t);function V(...e){return t=>e.some(r=>De(r,t))}function W(...e){return t=>e.every(r=>De(r,t))}function J(e,t){if(!e||!e.meta)return!1;let r=typeof e.meta.requestId=="string",n=t.indexOf(e.meta.requestStatus)>-1;return r&&n}function z(e){return typeof e[0]=="function"&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function Oe(...e){return e.length===0?t=>J(t,["pending"]):z(e)?V(...e.map(t=>t.pending)):Oe()(e[0])}function X(...e){return e.length===0?t=>J(t,["rejected"]):z(e)?V(...e.map(t=>t.rejected)):X()(e[0])}function Ne(...e){let t=r=>r&&r.meta&&r.meta.rejectedWithValue;return e.length===0?W(X(...e),t):z(e)?W(X(...e),t):Ne()(e[0])}function je(...e){return e.length===0?t=>J(t,["fulfilled"]):z(e)?V(...e.map(t=>t.fulfilled)):je()(e[0])}function Fe(...e){return e.length===0?t=>J(t,["pending","fulfilled","rejected"]):z(e)?V(...e.flatMap(t=>[t.pending,t.rejected,t.fulfilled])):Fe()(e[0])}var Dt="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",I=(e=21)=>{let t="",r=e;for(;r--;)t+=Dt[Math.random()*64|0];return t};var Ot=["name","message","stack","code"],G=class{constructor(t,r){this.payload=t;this.meta=r}_type},Q=class{constructor(t,r){this.payload=t;this.meta=r}_type},_e=e=>{if(typeof e=="object"&&e!==null){let t={};for(let r of Ot)typeof e[r]=="string"&&(t[r]=e[r]);return t}return{message:String(e)}},Ve="External signal was aborted",fe=(()=>{function e(t,r,n){let o=M(t+"/fulfilled",(c,l,i,d)=>({payload:c,meta:{...d||{},arg:i,requestId:l,requestStatus:"fulfilled"}})),a=M(t+"/pending",(c,l,i)=>({payload:void 0,meta:{...i||{},arg:l,requestId:c,requestStatus:"pending"}})),s=M(t+"/rejected",(c,l,i,d,T)=>({payload:d,error:(n&&n.serializeError||_e)(c||"Rejected"),meta:{...T||{},arg:i,requestId:l,rejectedWithValue:!!d,requestStatus:"rejected",aborted:c?.name==="AbortError",condition:c?.name==="ConditionError"}}));function y(c,{signal:l}={}){return(i,d,T)=>{let g=n?.idGenerator?n.idGenerator(c):I(),p=new AbortController,h,u;function f(A){u=A,p.abort()}l&&(l.aborted?f(Ve):l.addEventListener("abort",()=>f(Ve),{once:!0}));let S=async function(){let A;try{let k=n?.condition?.(c,{getState:d,extra:T});if(Nt(k)&&(k=await k),k===!1||p.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};let w=new Promise((C,E)=>{h=()=>{E({name:"AbortError",message:u||"Aborted"})},p.signal.addEventListener("abort",h)});i(a(g,c,n?.getPendingMeta?.({requestId:g,arg:c},{getState:d,extra:T}))),A=await Promise.race([w,Promise.resolve(r(c,{dispatch:i,getState:d,extra:T,requestId:g,signal:p.signal,abort:f,rejectWithValue:(C,E)=>new G(C,E),fulfillWithValue:(C,E)=>new Q(C,E)})).then(C=>{if(C instanceof G)throw C;return C instanceof Q?o(C.payload,g,c,C.meta):o(C,g,c)})])}catch(k){A=k instanceof G?s(null,g,c,k.payload,k.meta):s(k,g,c)}finally{h&&p.signal.removeEventListener("abort",h)}return n&&!n.dispatchConditionRejection&&s.match(A)&&A.meta.condition||i(A),A}();return Object.assign(S,{abort:f,requestId:g,arg:c,unwrap(){return S.then(Le)}})}}return Object.assign(y,{pending:a,rejected:s,fulfilled:o,settled:V(s,o),typePrefix:t})}return e.withTypes=()=>e,e})();function Le(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}function Nt(e){return e!==null&&typeof e=="object"&&typeof e.then=="function"}var Ue=Symbol.for("rtk-slice-createasyncthunk"),jt={[Ue]:fe},We=(n=>(n.reducer="reducer",n.reducerWithPrepare="reducerWithPrepare",n.asyncThunk="asyncThunk",n))(We||{});function Ft(e,t){return`${e}/${t}`}function ze({creators:e}={}){let t=e?.asyncThunk?.[Ue];return function(n){let{name:o,reducerPath:a=o}=n;if(!o)throw new Error(x(11));let s=(typeof n.reducers=="function"?n.reducers(Lt()):n.reducers)||{},y=Object.keys(s),c={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},l={addCase(A,m){let k=typeof A=="string"?A:A.type;if(!k)throw new Error(x(12));if(k in c.sliceCaseReducersByType)throw new Error(x(13));return c.sliceCaseReducersByType[k]=m,l},addMatcher(A,m){return c.sliceMatchers.push({matcher:A,reducer:m}),l},exposeAction(A,m){return c.actionCreators[A]=m,l},exposeCaseReducer(A,m){return c.sliceCaseReducersByName[A]=m,l}};y.forEach(A=>{let m=s[A],k={reducerName:A,type:Ft(o,A),createNotation:typeof n.reducers=="function"};Wt(m)?Gt(k,m,l,t):Ut(k,m,l)});function i(){let[A={},m=[],k=void 0]=typeof n.extraReducers=="function"?$(n.extraReducers):[n.extraReducers],w={...A,...c.sliceCaseReducersByType};return pe(n.initialState,C=>{for(let E in w)C.addCase(E,w[E]);for(let E of c.sliceMatchers)C.addMatcher(E.matcher,E.reducer);for(let E of m)C.addMatcher(E.matcher,E.reducer);k&&C.addDefaultCase(k)})}let d=A=>A,T=new Map,g=new WeakMap,p;function h(A,m){return p||(p=i()),p(A,m)}function u(){return p||(p=i()),p.getInitialState()}function f(A,m=!1){function k(C){let E=C[A];return typeof E>"u"&&m&&(E=P(g,k,u)),E}function w(C=d){let E=P(T,m,()=>new WeakMap);return P(E,C,()=>{let U={};for(let[H,j]of Object.entries(n.selectors??{}))U[H]=Vt(j,C,()=>P(g,C,u),m);return U})}return{reducerPath:A,getSelectors:w,get selectors(){return w(k)},selectSlice:k}}let S={name:o,reducer:h,actions:c.actionCreators,caseReducers:c.sliceCaseReducersByName,getInitialState:u,...f(a),injectInto(A,{reducerPath:m,...k}={}){let w=m??a;return A.inject({reducerPath:w,reducer:h},k),{...S,...f(w,!0)}}};return S}}function Vt(e,t,r,n){function o(a,...s){let y=t(a);return typeof y>"u"&&n&&(y=r()),e(y,...s)}return o.unwrapped=e,o}var _t=ze();function Lt(){function e(t,r){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...r}}return e.withTypes=()=>e,{reducer(t){return Object.assign({[t.name](...r){return t(...r)}}[t.name],{_reducerDefinitionType:"reducer"})},preparedReducer(t,r){return{_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:r}},asyncThunk:e}}function Ut({type:e,reducerName:t,createNotation:r},n,o){let a,s;if("reducer"in n){if(r&&!zt(n))throw new Error(x(17));a=n.reducer,s=n.prepare}else a=n;o.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,s?M(e,s):M(e))}function Wt(e){return e._reducerDefinitionType==="asyncThunk"}function zt(e){return e._reducerDefinitionType==="reducerWithPrepare"}function Gt({type:e,reducerName:t},r,n,o){if(!o)throw new Error(x(18));let{payloadCreator:a,fulfilled:s,pending:y,rejected:c,settled:l,options:i}=r,d=o(e,a,i);n.exposeAction(t,d),s&&n.addCase(d.fulfilled,s),y&&n.addCase(d.pending,y),c&&n.addCase(d.rejected,c),l&&n.addMatcher(d.settled,l),n.exposeCaseReducer(t,{fulfilled:s||Y,pending:y||Y,rejected:c||Y,settled:l||Y})}function Y(){}function Bt(){return{ids:[],entities:{}}}function Ge(e){function t(r={},n){let o=Object.assign(Bt(),r);return n?e.setAll(o,n):o}return{getInitialState:t}}function Be(){function e(t,r={}){let{createSelector:n=ie}=r,o=d=>d.ids,a=d=>d.entities,s=n(o,a,(d,T)=>d.map(g=>T[g])),y=(d,T)=>T,c=(d,T)=>d[T],l=n(o,d=>d.length);if(!t)return{selectIds:o,selectEntities:a,selectAll:s,selectTotal:l,selectById:n(a,y,c)};let i=n(t,a);return{selectIds:n(t,o),selectEntities:i,selectAll:n(t,s),selectTotal:n(t,l),selectById:n(i,y,c)}}return{getSelectors:e}}import{produce as Kt,isDraft as Ht}from"immer";var qt=Ht;function Ke(e){let t=R((r,n)=>e(n));return function(n){return t(n,void 0)}}function R(e){return function(r,n){function o(s){return ce(s)}let a=s=>{o(n)?e(n.payload,s):e(n,s)};return qt(r)?(a(r),r):Kt(r,a)}}import{current as $t,isDraft as Xt}from"immer";function D(e,t){return t(e)}function v(e){return Array.isArray(e)||(e=Object.values(e)),e}function B(e){return Xt(e)?$t(e):e}function Z(e,t,r){e=v(e);let n=B(r.ids),o=new Set(n),a=[],s=new Set([]),y=[];for(let c of e){let l=D(c,t);o.has(l)||s.has(l)?y.push({id:l,changes:c}):(s.add(l),a.push(c))}return[a,y,n]}function ee(e){function t(p,h){let u=D(p,e);u in h.entities||(h.ids.push(u),h.entities[u]=p)}function r(p,h){p=v(p);for(let u of p)t(u,h)}function n(p,h){let u=D(p,e);u in h.entities||h.ids.push(u),h.entities[u]=p}function o(p,h){p=v(p);for(let u of p)n(u,h)}function a(p,h){p=v(p),h.ids=[],h.entities={},r(p,h)}function s(p,h){return y([p],h)}function y(p,h){let u=!1;p.forEach(f=>{f in h.entities&&(delete h.entities[f],u=!0)}),u&&(h.ids=h.ids.filter(f=>f in h.entities))}function c(p){Object.assign(p,{ids:[],entities:{}})}function l(p,h,u){let f=u.entities[h.id];if(f===void 0)return!1;let S=Object.assign({},f,h.changes),A=D(S,e),m=A!==h.id;return m&&(p[h.id]=A,delete u.entities[h.id]),u.entities[A]=S,m}function i(p,h){return d([p],h)}function d(p,h){let u={},f={};p.forEach(A=>{A.id in h.entities&&(f[A.id]={id:A.id,changes:{...f[A.id]?.changes,...A.changes}})}),p=Object.values(f),p.length>0&&p.filter(m=>l(u,m,h)).length>0&&(h.ids=Object.values(h.entities).map(m=>D(m,e)))}function T(p,h){return g([p],h)}function g(p,h){let[u,f]=Z(p,e,h);r(u,h),d(f,h)}return{removeAll:Ke(c),addOne:R(t),addMany:R(r),setOne:R(n),setMany:R(o),setAll:R(a),updateOne:R(i),updateMany:R(d),upsertOne:R(T),upsertMany:R(g),removeOne:R(s),removeMany:R(y)}}function Jt(e,t,r){let n=0,o=e.length;for(;n<o;){let a=n+o>>>1,s=e[a];r(t,s)>=0?n=a+1:o=a}return n}function Qt(e,t,r){let n=Jt(e,t,r);return e.splice(n,0,t),e}function He(e,t){let{removeOne:r,removeMany:n,removeAll:o}=ee(e);function a(u,f){return s([u],f)}function s(u,f,S){u=v(u);let A=new Set(S??B(f.ids)),m=u.filter(k=>!A.has(D(k,e)));m.length!==0&&h(f,m)}function y(u,f){return c([u],f)}function c(u,f){if(u=v(u),u.length!==0){for(let S of u)delete f.entities[e(S)];h(f,u)}}function l(u,f){u=v(u),f.entities={},f.ids=[],s(u,f,[])}function i(u,f){return d([u],f)}function d(u,f){let S=!1,A=!1;for(let m of u){let k=f.entities[m.id];if(!k)continue;S=!0,Object.assign(k,m.changes);let w=e(k);if(m.id!==w){A=!0,delete f.entities[m.id];let C=f.ids.indexOf(m.id);f.ids[C]=w,f.entities[w]=k}}S&&h(f,[],S,A)}function T(u,f){return g([u],f)}function g(u,f){let[S,A,m]=Z(u,e,f);S.length&&s(S,f,m),A.length&&d(A,f)}function p(u,f){if(u.length!==f.length)return!1;for(let S=0;S<u.length;S++)if(u[S]!==f[S])return!1;return!0}let h=(u,f,S,A)=>{let m=B(u.entities),k=B(u.ids),w=u.entities,C=k;A&&(C=new Set(k));let E=[];for(let j of C){let Se=m[j];Se&&E.push(Se)}let U=E.length===0;for(let j of f)w[e(j)]=j,U||Qt(E,j,t);U?E=f.slice().sort(t):S&&E.sort(t);let H=E.map(e);p(k,H)||(u.ids=H)};return{removeOne:r,removeMany:n,removeAll:o,addOne:R(a),updateOne:R(i),upsertOne:R(T),setOne:R(y),setMany:R(c),setAll:R(l),addMany:R(s),updateMany:R(d),upsertMany:R(g)}}function Yt(e={}){let{selectId:t,sortComparer:r}={sortComparer:!1,selectId:s=>s.id,...e},n=r?He(t,r):ee(t),o=Ge(n),a=Be();return{selectId:t,sortComparer:r,...o,...a,...n}}import{isAction as en}from"redux";var Zt="task",qe="listener",$e="completed",ye="cancelled",Xe=`task-${ye}`,Je=`task-${$e}`,te=`${qe}-${ye}`,Qe=`${qe}-${$e}`,b=class{constructor(t){this.code=t;this.message=`${Zt} ${ye} (reason: ${t})`}name="TaskAbortError";message};var ne=(e,t)=>{if(typeof e!="function")throw new TypeError(x(32))},_=()=>{},re=(e,t=_)=>(e.catch(t),e),oe=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),O=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))};var N=e=>{if(e.aborted){let{reason:t}=e;throw new b(t)}};function he(e,t){let r=_;return new Promise((n,o)=>{let a=()=>o(new b(e.reason));if(e.aborted){a();return}r=oe(e,a),t.finally(()=>r()).then(n,o)}).finally(()=>{r=_})}var Ye=async(e,t)=>{try{return await Promise.resolve(),{status:"ok",value:await e()}}catch(r){return{status:r instanceof b?"cancelled":"rejected",error:r}}finally{t?.()}},K=e=>t=>re(he(e,t).then(r=>(N(e),r))),Ae=e=>{let t=K(e);return r=>t(new Promise(n=>setTimeout(n,r)))};var{assign:L}=Object,Ze={},ae="listenerMiddleware",tn=(e,t)=>{let r=n=>oe(e,()=>O(n,e.reason));return(n,o)=>{ne(n,"taskExecutor");let a=new AbortController;r(a);let s=Ye(async()=>{N(e),N(a.signal);let y=await n({pause:K(a.signal),delay:Ae(a.signal),signal:a.signal});return N(a.signal),y},()=>O(a,Je));return o?.autoJoin&&t.push(s.catch(_)),{result:K(e)(s),cancel(){O(a,Xe)}}}},nn=(e,t)=>{let r=async(n,o)=>{N(t);let a=()=>{},y=[new Promise((c,l)=>{let i=e({predicate:n,effect:(d,T)=>{T.unsubscribe(),c([d,T.getState(),T.getOriginalState()])}});a=()=>{i(),l()}})];o!=null&&y.push(new Promise(c=>setTimeout(c,o,null)));try{let c=await he(t,Promise.race(y));return N(t),c}finally{a()}};return(n,o)=>re(r(n,o))},nt=e=>{let{type:t,actionCreator:r,matcher:n,predicate:o,effect:a}=e;if(t)o=M(t).match;else if(r)t=r.type,o=r.match;else if(n)o=n;else if(!o)throw new Error(x(21));return ne(a,"options.listener"),{predicate:o,type:t,effect:a}},rt=L(e=>{let{type:t,predicate:r,effect:n}=nt(e);return{id:I(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw new Error(x(22))}}},{withTypes:()=>rt}),et=(e,t)=>{let{type:r,effect:n,predicate:o}=nt(t);return Array.from(e.values()).find(a=>(typeof r=="string"?a.type===r:a.predicate===o)&&a.effect===n)},Te=e=>{e.pending.forEach(t=>{O(t,te)})},rn=e=>()=>{e.forEach(Te),e.clear()},tt=(e,t,r)=>{try{e(t,r)}catch(n){setTimeout(()=>{throw n},0)}},me=L(M(`${ae}/add`),{withTypes:()=>me}),ot=M(`${ae}/removeAll`),ge=L(M(`${ae}/remove`),{withTypes:()=>ge}),on=(...e)=>{console.error(`${ae}/error`,...e)},an=(e={})=>{let t=new Map,{extra:r,onError:n=on}=e;ne(n,"onError");let o=i=>(i.unsubscribe=()=>t.delete(i.id),t.set(i.id,i),d=>{i.unsubscribe(),d?.cancelActive&&Te(i)}),a=i=>{let d=et(t,i)??rt(i);return o(d)};L(a,{withTypes:()=>a});let s=i=>{let d=et(t,i);return d&&(d.unsubscribe(),i.cancelActive&&Te(d)),!!d};L(s,{withTypes:()=>s});let y=async(i,d,T,g)=>{let p=new AbortController,h=nn(a,p.signal),u=[];try{i.pending.add(p),await Promise.resolve(i.effect(d,L({},T,{getOriginalState:g,condition:(f,S)=>h(f,S).then(Boolean),take:h,delay:Ae(p.signal),pause:K(p.signal),extra:r,signal:p.signal,fork:tn(p.signal,u),unsubscribe:i.unsubscribe,subscribe:()=>{t.set(i.id,i)},cancelActiveListeners:()=>{i.pending.forEach((f,S,A)=>{f!==p&&(O(f,te),A.delete(f))})},cancel:()=>{O(p,te),i.pending.delete(p)},throwIfCancelled:()=>{N(p.signal)}})))}catch(f){f instanceof b||tt(n,f,{raisedBy:"effect"})}finally{await Promise.all(u),O(p,Qe),i.pending.delete(p)}},c=rn(t);return{middleware:i=>d=>T=>{if(!en(T))return d(T);if(me.match(T))return a(T.payload);if(ot.match(T)){c();return}if(ge.match(T))return s(T.payload);let g=i.getState(),p=()=>{if(g===Ze)throw new Error(x(23));return g},h;try{if(h=d(T),t.size>0){let u=i.getState(),f=Array.from(t.values());for(let S of f){let A=!1;try{A=S.predicate(T,u,g)}catch(m){A=!1,tt(n,m,{raisedBy:"predicate"})}A&&y(S,T,i,p)}}}finally{g=Ze}return h},startListening:a,stopListening:s,clearListeners:c}};import{compose as sn}from"redux";var cn=e=>({middleware:e,applied:new Map}),dn=e=>t=>t?.meta?.instanceId===e,un=()=>{let e=I(),t=new Map,r=Object.assign(M("dynamicMiddleware/add",(...y)=>({payload:y,meta:{instanceId:e}})),{withTypes:()=>r}),n=Object.assign(function(...c){c.forEach(l=>{P(t,l,cn)})},{withTypes:()=>n}),o=y=>{let c=Array.from(t.values()).map(l=>P(l.applied,y,l.middleware));return sn(...c)},a=W(r,dn(e));return{middleware:y=>c=>l=>a(l)?(n(...l.payload),y.dispatch):o(y)(c)(l),addMiddleware:n,withMiddleware:r,instanceId:e}};import{combineReducers as ln}from"redux";var pn=e=>"reducerPath"in e&&typeof e.reducerPath=="string",fn=e=>e.flatMap(t=>pn(t)?[[t.reducerPath,t.reducer]]:Object.entries(t)),ke=Symbol.for("rtk-state-proxy-original"),yn=e=>!!e&&!!e[ke],hn=new WeakMap,An=(e,t,r)=>P(hn,e,()=>new Proxy(e,{get:(n,o,a)=>{if(o===ke)return n;let s=Reflect.get(n,o,a);if(typeof s>"u"){let y=r[o];if(typeof y<"u")return y;let c=t[o];if(c){let l=c(void 0,{type:I()});if(typeof l>"u")throw new Error(x(24));return r[o]=l,l}}return s}})),Tn=e=>{if(!yn(e))throw new Error(x(25));return e[ke]},mn={},gn=(e=mn)=>e;function kn(...e){let t=Object.fromEntries(fn(e)),r=()=>Object.keys(t).length?ln(t):gn,n=r();function o(c,l){return n(c,l)}o.withLazyLoadedSlices=()=>o;let a={},s=(c,l={})=>{let{reducerPath:i,reducer:d}=c,T=t[i];return!l.overrideExisting&&T&&T!==d||(l.overrideExisting&&T!==d&&delete a[i],t[i]=d,n=r()),o},y=Object.assign(function(l,i){return function(T,...g){return l(An(i?i(T,...g):T,t,a),...g)}},{original:Tn});return Object.assign(o,{inject:s,selector:y})}function x(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}export{We as ReducerType,ue as SHOULD_AUTOBATCH,b as TaskAbortError,F as Tuple,me as addListener,jt as asyncThunkCreator,le as autoBatchEnhancer,ze as buildCreateSlice,ot as clearAllListeners,kn as combineSlices,Mt as configureStore,M as createAction,lt as createActionCreatorInvariantMiddleware,fe as createAsyncThunk,ie as createDraftSafeSelector,xe as createDraftSafeSelectorCreator,un as createDynamicMiddleware,Yt as createEntityAdapter,ht as createImmutableStateInvariantMiddleware,an as createListenerMiddleware,vo as createNextState,pe as createReducer,Vo as createSelector,_o as createSelectorCreator,Tt as createSerializableStateInvariantMiddleware,_t as createSlice,Do as current,Me as findNonSerializableValue,x as formatProdErrorMessage,Oo as freeze,se as isActionCreator,W as isAllOf,V as isAnyOf,Fe as isAsyncThunkAction,jo as isDraft,ce as isFluxStandardAction,je as isFulfilled,yt as isImmutableDefault,Oe as isPending,we as isPlain,X as isRejected,Ne as isRejectedWithValue,Lo as lruMemoize,_e as miniSerializeError,I as nanoid,No as original,St as prepareAutoBatched,ge as removeListener,Le as unwrapResult,Uo as weakMapMemoize};
//# sourceMappingURL=redux-toolkit.browser.mjs.map