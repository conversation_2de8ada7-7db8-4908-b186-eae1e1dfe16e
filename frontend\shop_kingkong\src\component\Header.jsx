import React from "react";
import { Link } from "react-router-dom";
import menuItems from "../data/menuItems";

const Header = () => {
  return (
    <header className="bg-white shadow-md">
      <div className="container px-8 py-6 mx-auto items-center justify-between">
        <div className="flex items-center">
          <img />
        </div>

        <nav className="flex space-x-12">
          {menuItems.map((item) => (
            <a
              key={item.label}
              href={item.href}
              className="text-gray-600 hover:text-blue-600 "
            >
              {item.label}
            </a>
          ))}
        </nav>
      </div>
    </header>
  );
};

export default Header;
