"use strict";var Oe=Object.defineProperty;var Ft=Object.getOwnPropertyDescriptor;var vt=Object.getOwnPropertyNames;var Ot=Object.prototype.hasOwnProperty;var Nt=(e,t)=>{for(var p in t)Oe(e,p,{get:t[p],enumerable:!0})},qt=(e,t,p,g)=>{if(t&&typeof t=="object"||typeof t=="function")for(let c of vt(t))!Ot.call(e,c)&&c!==p&&Oe(e,c,{get:()=>t[c],enumerable:!(g=Ft(t,c))||g.enumerable});return e};var Kt=e=>qt(Oe({},"__esModule",{value:!0}),e);var Jt={};Nt(Jt,{NamedSchemaError:()=>oe,QueryStatus:()=>Ee,_NEVER:()=>Tt,buildCreateApi:()=>Fe,copyWithStructuralSharing:()=>me,coreModule:()=>ve,coreModuleName:()=>De,createApi:()=>Mt,defaultSerializeQueryArgs:()=>xe,fakeBaseQuery:()=>ht,fetchBaseQuery:()=>Xe,retry:()=>et,setupListeners:()=>tt,skipToken:()=>Se});module.exports=Kt(Jt);var Ee=(c=>(c.uninitialized="uninitialized",c.pending="pending",c.fulfilled="fulfilled",c.rejected="rejected",c))(Ee||{});function Ne(e){return{status:e,isUninitialized:e==="uninitialized",isLoading:e==="pending",isSuccess:e==="fulfilled",isError:e==="rejected"}}var o=require("@reduxjs/toolkit");var Ve=o.isPlainObject;function me(e,t){if(e===t||!(Ve(e)&&Ve(t)||Array.isArray(e)&&Array.isArray(t)))return t;let p=Object.keys(t),g=Object.keys(e),c=p.length===g.length,A=Array.isArray(t)?[]:{};for(let h of p)A[h]=me(e[h],t[h]),c&&(c=e[h]===A[h]);return c?e:A}function Pe(e){let t=0;for(let p in e)t++;return t}var qe=e=>[].concat(...e);function _e(e){return new RegExp("(^|:)//").test(e)}function ze(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}function re(e){return e!=null}function We(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}var Ut=e=>e.replace(/\/$/,""),Lt=e=>e.replace(/^\//,"");function $e(e,t){if(!e)return t;if(!t)return e;if(_e(t))return t;let p=e.endsWith("/")||!t.startsWith("?")?"/":"";return e=Ut(e),t=Lt(t),`${e}${p}${t}`}function Je(e,t,p){return e.has(t)?e.get(t):e.set(t,p).get(t)}function ge(e,t,p){return e.has(t)?e.get(t):e.set(t,p(t)).get(t)}var Qe=()=>new Map;var Ge=(...e)=>fetch(...e),jt=e=>e.status>=200&&e.status<=299,Ht=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function Ye(e){if(!(0,o.isPlainObject)(e))return e;let t={...e};for(let[p,g]of Object.entries(t))g===void 0&&delete t[p];return t}function Xe({baseUrl:e,prepareHeaders:t=D=>D,fetchFn:p=Ge,paramsSerializer:g,isJsonContentType:c=Ht,jsonContentType:A="application/json",jsonReplacer:h,timeout:w,responseHandler:I,validateStatus:x,...E}={}){return typeof fetch>"u"&&p===Ge&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(R,k,C)=>{let{getState:P,extra:b,endpoint:r,forced:m,type:i}=k,u,{url:l,headers:T=new Headers(E.headers),params:S=void 0,responseHandler:Q=I??"json",validateStatus:d=x??jt,timeout:y=w,...n}=typeof R=="string"?{url:R}:R,a,s=k.signal;y&&(a=new AbortController,k.signal.addEventListener("abort",a.abort),s=a.signal);let f={...E,signal:s,...n};T=new Headers(Ye(T)),f.headers=await t(T,{getState:P,arg:R,extra:b,endpoint:r,forced:m,type:i,extraOptions:C})||T;let B=K=>typeof K=="object"&&((0,o.isPlainObject)(K)||Array.isArray(K)||typeof K.toJSON=="function");if(!f.headers.has("content-type")&&B(f.body)&&f.headers.set("content-type",A),B(f.body)&&c(f.headers)&&(f.body=JSON.stringify(f.body,h)),S){let K=~l.indexOf("?")?"&":"?",L=g?g(S):new URLSearchParams(Ye(S));l+=K+L}l=$e(e,l);let v=new Request(l,f);u={request:new Request(l,f)};let F,M=!1,N=a&&setTimeout(()=>{M=!0,a.abort()},y);try{F=await p(v)}catch(K){return{error:{status:M?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(K)},meta:u}}finally{N&&clearTimeout(N),a?.signal.removeEventListener("abort",a.abort)}let V=F.clone();u.response=V;let z,j="";try{let K;if(await Promise.all([D(F,Q).then(L=>z=L,L=>K=L),V.text().then(L=>j=L,()=>{})]),K)throw K}catch(K){return{error:{status:"PARSING_ERROR",originalStatus:F.status,data:j,error:String(K)},meta:u}}return d(F,z)?{data:z,meta:u}:{error:{status:F.status,data:z},meta:u}};async function D(R,k){if(typeof k=="function")return k(R);if(k==="content-type"&&(k=c(R.headers)?"json":"text"),k==="json"){let C=await R.text();return C.length?JSON.parse(C):null}return R.text()}}var J=class{constructor(t,p=void 0){this.value=t;this.meta=p}};async function Vt(e=0,t=5){let p=Math.min(e,t),g=~~((Math.random()+.4)*(300<<p));await new Promise(c=>setTimeout(A=>c(A),g))}function _t(e,t){throw Object.assign(new J({error:e,meta:t}),{throwImmediately:!0})}var Ze={},zt=(e,t)=>async(p,g,c)=>{let A=[5,(t||Ze).maxRetries,(c||Ze).maxRetries].filter(E=>E!==void 0),[h]=A.slice(-1),I={maxRetries:h,backoff:Vt,retryCondition:(E,D,{attempt:R})=>R<=h,...t,...c},x=0;for(;;)try{let E=await e(p,g,c);if(E.error)throw new J(E);return E}catch(E){if(x++,E.throwImmediately){if(E instanceof J)return E.value;throw E}if(E instanceof J&&!I.retryCondition(E.value.error,p,{attempt:x,baseQueryApi:g,extraOptions:c}))return E.value;await I.backoff(x,I.maxRetries)}},et=Object.assign(zt,{fail:_t});var Z=(0,o.createAction)("__rtkq/focused"),de=(0,o.createAction)("__rtkq/unfocused"),ee=(0,o.createAction)("__rtkq/online"),ce=(0,o.createAction)("__rtkq/offline"),Ke=!1;function tt(e,t){function p(){let g=()=>e(Z()),c=()=>e(de()),A=()=>e(ee()),h=()=>e(ce()),w=()=>{window.document.visibilityState==="visible"?g():c()};return Ke||typeof window<"u"&&window.addEventListener&&(window.addEventListener("visibilitychange",w,!1),window.addEventListener("focus",g,!1),window.addEventListener("online",A,!1),window.addEventListener("offline",h,!1),Ke=!0),()=>{window.removeEventListener("focus",g),window.removeEventListener("visibilitychange",w),window.removeEventListener("online",A),window.removeEventListener("offline",h),Ke=!1}}return t?t(e,{onFocus:Z,onFocusLost:de,onOffline:ce,onOnline:ee}):p()}function ie(e){return e.type==="query"}function nt(e){return e.type==="mutation"}function ae(e){return e.type==="infinitequery"}function le(e){return ie(e)||ae(e)}function Te(e,t,p,g,c,A){return Wt(e)?e(t,p,g,c).filter(re).map(Ie).map(A):Array.isArray(e)?e.map(Ie).map(A):[]}function Wt(e){return typeof e=="function"}function Ie(e){return typeof e=="string"?{type:e}:e}var Be=require("immer");var Sn=require("@reduxjs/toolkit");function rt(e,t){return e.catch(t)}var fe=Symbol("forceQueryFn"),he=e=>typeof e[fe]=="function";function it({serializeQueryArgs:e,queryThunk:t,infiniteQueryThunk:p,mutationThunk:g,api:c,context:A,internalState:h}){let{runningQueries:w,runningMutations:I}=h,{unsubscribeQueryResult:x,removeMutationResult:E,updateSubscriptionOptions:D}=c.internalActions;return{buildInitiateQuery:m,buildInitiateInfiniteQuery:i,buildInitiateMutation:u,getRunningQueryThunk:R,getRunningMutationThunk:k,getRunningQueriesThunk:C,getRunningMutationsThunk:P};function R(l,T){return S=>{let Q=A.endpointDefinitions[l],d=e({queryArgs:T,endpointDefinition:Q,endpointName:l});return w.get(S)?.[d]}}function k(l,T){return S=>I.get(S)?.[T]}function C(){return l=>Object.values(w.get(l)||{}).filter(re)}function P(){return l=>Object.values(I.get(l)||{}).filter(re)}function b(l){}function r(l,T){let S=(Q,{subscribe:d=!0,forceRefetch:y,subscriptionOptions:n,[fe]:a,...s}={})=>(f,B)=>{let v=e({queryArgs:Q,endpointDefinition:T,endpointName:l}),U,F={...s,type:"query",subscribe:d,forceRefetch:y,subscriptionOptions:n,endpointName:l,originalArgs:Q,queryCacheKey:v,[fe]:a};if(ie(T))U=t(F);else{let{direction:O,initialPageParam:q}=s;U=p({...F,direction:O,initialPageParam:q})}let M=c.endpoints[l].select(Q),N=f(U),V=M(B());let{requestId:z,abort:j}=N,K=V.requestId!==z,L=w.get(f)?.[v],H=()=>M(B()),_=Object.assign(a?N.then(H):K&&!L?Promise.resolve(V):Promise.all([L,N]).then(H),{arg:Q,requestId:z,subscriptionOptions:n,queryCacheKey:v,abort:j,async unwrap(){let O=await _;if(O.isError)throw O.error;return O.data},refetch:()=>f(S(Q,{subscribe:!1,forceRefetch:!0})),unsubscribe(){d&&f(x({queryCacheKey:v,requestId:z}))},updateSubscriptionOptions(O){_.subscriptionOptions=O,f(D({endpointName:l,requestId:z,queryCacheKey:v,options:O}))}});if(!L&&!K&&!a){let O=Je(w,f,{});O[v]=_,_.then(()=>{delete O[v],Pe(O)||w.delete(f)})}return _};return S}function m(l,T){return r(l,T)}function i(l,T){return r(l,T)}function u(l){return(T,{track:S=!0,fixedCacheKey:Q}={})=>(d,y)=>{let n=g({type:"mutation",endpointName:l,originalArgs:T,track:S,fixedCacheKey:Q}),a=d(n);let{requestId:s,abort:f,unwrap:B}=a,v=rt(a.unwrap().then(N=>({data:N})),N=>({error:N})),U=()=>{d(E({requestId:s,fixedCacheKey:Q}))},F=Object.assign(v,{arg:a.arg,requestId:s,abort:f,unwrap:B,reset:U}),M=I.get(d)||{};return I.set(d,M),M[s]=F,F.then(()=>{delete M[s],Pe(M)||I.delete(d)}),Q&&(M[Q]=F,F.then(()=>{M[Q]===F&&(delete M[Q],Pe(M)||I.delete(d))})),F}}}var at=require("@standard-schema/utils"),oe=class extends at.SchemaError{constructor(p,g,c,A){super(p);this.value=g;this.schemaName=c;this._bqMeta=A}},te=(e,t)=>Array.isArray(e)?e.includes(t):!!e;async function ne(e,t,p,g){let c=await e["~standard"].validate(t);if(c.issues)throw new oe(c.issues,t,p,g);return c.value}function ot(e){return e}var Re=(e={})=>({...e,[o.SHOULD_AUTOBATCH]:!0});function st({reducerPath:e,baseQuery:t,context:{endpointDefinitions:p},serializeQueryArgs:g,api:c,assertTagType:A,selectors:h,onSchemaFailure:w,catchSchemaFailure:I,skipSchemaValidation:x}){let E=(n,a,s,f)=>(B,v)=>{let U=p[n],F=g({queryArgs:a,endpointDefinition:U,endpointName:n});if(B(c.internalActions.queryResultPatched({queryCacheKey:F,patches:s})),!f)return;let M=c.endpoints[n].select(a)(v()),N=Te(U.providesTags,M.data,void 0,a,{},A);B(c.internalActions.updateProvidedBy([{queryCacheKey:F,providedTags:N}]))};function D(n,a,s=0){let f=[a,...n];return s&&f.length>s?f.slice(0,-1):f}function R(n,a,s=0){let f=[...n,a];return s&&f.length>s?f.slice(1):f}let k=(n,a,s,f=!0)=>(B,v)=>{let F=c.endpoints[n].select(a)(v()),M={patches:[],inversePatches:[],undo:()=>B(c.util.patchQueryData(n,a,M.inversePatches,f))};if(F.status==="uninitialized")return M;let N;if("data"in F)if((0,Be.isDraftable)(F.data)){let[V,z,j]=(0,Be.produceWithPatches)(F.data,s);M.patches.push(...z),M.inversePatches.push(...j),N=V}else N=s(F.data),M.patches.push({op:"replace",path:[],value:N}),M.inversePatches.push({op:"replace",path:[],value:F.data});return M.patches.length===0||B(c.util.patchQueryData(n,a,M.patches,f)),M},C=(n,a,s)=>f=>f(c.endpoints[n].initiate(a,{subscribe:!1,forceRefetch:!0,[fe]:()=>({data:s})})),P=(n,a)=>n.query&&n[a]?n[a]:ot,b=async(n,{signal:a,abort:s,rejectWithValue:f,fulfillWithValue:B,dispatch:v,getState:U,extra:F})=>{let M=p[n.endpointName],{metaSchema:N,skipSchemaValidation:V=x}=M;try{let z=ot,j={signal:a,abort:s,dispatch:v,getState:U,extra:F,endpoint:n.endpointName,type:n.type,forced:n.type==="query"?r(n,U()):void 0,queryCacheKey:n.type==="query"?n.queryCacheKey:void 0},K=n.type==="query"?n[fe]:void 0,L,H=async(O,q,W,ue)=>{if(q==null&&O.pages.length)return Promise.resolve({data:O});let Y={queryArg:n.originalArgs,pageParam:q},ye=await _(Y),$=ue?D:R;return{data:{pages:$(O.pages,ye.data,W),pageParams:$(O.pageParams,q,W)},meta:ye.meta}};async function _(O){let q,{extraOptions:W,argSchema:ue,rawResponseSchema:Y,responseSchema:ye}=M;if(ue&&!te(V,"arg")&&(O=await ne(ue,O,"argSchema",{})),K?q=K():M.query?(z=P(M,"transformResponse"),q=await t(M.query(O),j,W)):q=await M.queryFn(O,j,W,pe=>t(pe,j,W)),typeof process<"u",q.error)throw new J(q.error,q.meta);let{data:$}=q;Y&&!te(V,"rawResponse")&&($=await ne(Y,q.data,"rawResponseSchema",q.meta));let X=await z($,q.meta,O);return ye&&!te(V,"response")&&(X=await ne(ye,X,"responseSchema",q.meta)),{...q,data:X}}if(n.type==="query"&&"infiniteQueryOptions"in M){let{infiniteQueryOptions:O}=M,{maxPages:q=1/0}=O,W,ue={pages:[],pageParams:[]},Y=h.selectQueryEntry(U(),n.queryCacheKey)?.data,$=r(n,U())&&!n.direction||!Y?ue:Y;if("direction"in n&&n.direction&&$.pages.length){let X=n.direction==="backward",be=(X?Ue:ke)(O,$,n.originalArgs);W=await H($,be,q,X)}else{let{initialPageParam:X=O.initialPageParam}=n,pe=Y?.pageParams??[],be=pe[0]??X,je=pe.length;W=await H($,be,q),K&&(W={data:W.data.pages[0]});for(let He=1;He<je;He++){let Ct=ke(O,W.data,n.originalArgs);W=await H(W.data,Ct,q)}}L=W}else L=await _(n.originalArgs);return N&&!te(V,"meta")&&L.meta&&(L.meta=await ne(N,L.meta,"metaSchema",L.meta)),B(L.data,Re({fulfilledTimeStamp:Date.now(),baseQueryMeta:L.meta}))}catch(z){let j=z;if(j instanceof J){let K=P(M,"transformErrorResponse"),{rawErrorResponseSchema:L,errorResponseSchema:H}=M,{value:_,meta:O}=j;try{L&&!te(V,"rawErrorResponse")&&(_=await ne(L,_,"rawErrorResponseSchema",O)),N&&!te(V,"meta")&&(O=await ne(N,O,"metaSchema",O));let q=await K(_,O,n.originalArgs);return H&&!te(V,"errorResponse")&&(q=await ne(H,q,"errorResponseSchema",O)),f(q,Re({baseQueryMeta:O}))}catch(q){j=q}}try{if(j instanceof oe){let K={endpoint:n.endpointName,arg:n.originalArgs,type:n.type,queryCacheKey:n.type==="query"?n.queryCacheKey:void 0};M.onSchemaFailure?.(j,K),w?.(j,K);let{catchSchemaFailure:L=I}=M;if(L)return f(L(j,K),Re({baseQueryMeta:j._bqMeta}))}}catch(K){j=K}throw typeof process<"u",console.error(j),j}};function r(n,a){let s=h.selectQueryEntry(a,n.queryCacheKey),f=h.selectConfig(a).refetchOnMountOrArgChange,B=s?.fulfilledTimeStamp,v=n.forceRefetch??(n.subscribe&&f);return v?v===!0||(Number(new Date)-Number(B))/1e3>=v:!1}let m=()=>(0,o.createAsyncThunk)(`${e}/executeQuery`,b,{getPendingMeta({arg:a}){let s=p[a.endpointName];return Re({startedTimeStamp:Date.now(),...ae(s)?{direction:a.direction}:{}})},condition(a,{getState:s}){let f=s(),B=h.selectQueryEntry(f,a.queryCacheKey),v=B?.fulfilledTimeStamp,U=a.originalArgs,F=B?.originalArgs,M=p[a.endpointName],N=a.direction;return he(a)?!0:B?.status==="pending"?!1:r(a,f)||ie(M)&&M?.forceRefetch?.({currentArg:U,previousArg:F,endpointState:B,state:f})?!0:!(v&&!N)},dispatchConditionRejection:!0}),i=m(),u=m(),l=(0,o.createAsyncThunk)(`${e}/executeMutation`,b,{getPendingMeta(){return Re({startedTimeStamp:Date.now()})}}),T=n=>"force"in n,S=n=>"ifOlderThan"in n,Q=(n,a,s)=>(f,B)=>{let v=T(s)&&s.force,U=S(s)&&s.ifOlderThan,F=(N=!0)=>{let V={forceRefetch:N,isPrefetch:!0};return c.endpoints[n].initiate(a,V)},M=c.endpoints[n].select(a)(B());if(v)f(F());else if(U){let N=M?.fulfilledTimeStamp;if(!N){f(F());return}(Number(new Date)-Number(new Date(N)))/1e3>=U&&f(F())}else f(F(!1))};function d(n){return a=>a?.meta?.arg?.endpointName===n}function y(n,a){return{matchPending:(0,o.isAllOf)((0,o.isPending)(n),d(a)),matchFulfilled:(0,o.isAllOf)((0,o.isFulfilled)(n),d(a)),matchRejected:(0,o.isAllOf)((0,o.isRejected)(n),d(a))}}return{queryThunk:i,mutationThunk:l,infiniteQueryThunk:u,prefetch:Q,updateQueryData:k,upsertQueryData:C,patchQueryData:E,buildMatchThunkActions:y}}function ke(e,{pages:t,pageParams:p},g){let c=t.length-1;return e.getNextPageParam(t[c],t,p[c],p,g)}function Ue(e,{pages:t,pageParams:p},g){return e.getPreviousPageParam?.(t[0],t,p[0],p,g)}function we(e,t,p,g){return Te(p[e.meta.arg.endpointName][t],(0,o.isFulfilled)(e)?e.payload:void 0,(0,o.isRejectedWithValue)(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,g)}var yt=require("immer"),Ae=require("immer");function Me(e,t,p){let g=e[t];g&&p(g)}function se(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function ut(e,t,p){let g=e[se(t)];g&&p(g)}var Ce={};function pt({reducerPath:e,queryThunk:t,mutationThunk:p,serializeQueryArgs:g,context:{endpointDefinitions:c,apiUid:A,extractRehydrationInfo:h,hasRehydrationInfo:w},assertTagType:I,config:x}){let E=(0,o.createAction)(`${e}/resetApiState`);function D(d,y,n,a){d[y.queryCacheKey]??={status:"uninitialized",endpointName:y.endpointName},Me(d,y.queryCacheKey,s=>{s.status="pending",s.requestId=n&&s.requestId?s.requestId:a.requestId,y.originalArgs!==void 0&&(s.originalArgs=y.originalArgs),s.startedTimeStamp=a.startedTimeStamp;let f=c[a.arg.endpointName];ae(f)&&"direction"in y&&(s.direction=y.direction)})}function R(d,y,n,a){Me(d,y.arg.queryCacheKey,s=>{if(s.requestId!==y.requestId&&!a)return;let{merge:f}=c[y.arg.endpointName];if(s.status="fulfilled",f)if(s.data!==void 0){let{fulfilledTimeStamp:B,arg:v,baseQueryMeta:U,requestId:F}=y,M=(0,o.createNextState)(s.data,N=>f(N,n,{arg:v.originalArgs,baseQueryMeta:U,fulfilledTimeStamp:B,requestId:F}));s.data=M}else s.data=n;else s.data=c[y.arg.endpointName].structuralSharing??!0?me((0,yt.isDraft)(s.data)?(0,Ae.original)(s.data):s.data,n):n;delete s.error,s.fulfilledTimeStamp=y.fulfilledTimeStamp})}let k=(0,o.createSlice)({name:`${e}/queries`,initialState:Ce,reducers:{removeQueryResult:{reducer(d,{payload:{queryCacheKey:y}}){delete d[y]},prepare:(0,o.prepareAutoBatched)()},cacheEntriesUpserted:{reducer(d,y){for(let n of y.payload){let{queryDescription:a,value:s}=n;D(d,a,!0,{arg:a,requestId:y.meta.requestId,startedTimeStamp:y.meta.timestamp}),R(d,{arg:a,requestId:y.meta.requestId,fulfilledTimeStamp:y.meta.timestamp,baseQueryMeta:{}},s,!0)}},prepare:d=>({payload:d.map(a=>{let{endpointName:s,arg:f,value:B}=a,v=c[s];return{queryDescription:{type:"query",endpointName:s,originalArgs:a.arg,queryCacheKey:g({queryArgs:f,endpointDefinition:v,endpointName:s})},value:B}}),meta:{[o.SHOULD_AUTOBATCH]:!0,requestId:(0,o.nanoid)(),timestamp:Date.now()}})},queryResultPatched:{reducer(d,{payload:{queryCacheKey:y,patches:n}}){Me(d,y,a=>{a.data=(0,Ae.applyPatches)(a.data,n.concat())})},prepare:(0,o.prepareAutoBatched)()}},extraReducers(d){d.addCase(t.pending,(y,{meta:n,meta:{arg:a}})=>{let s=he(a);D(y,a,s,n)}).addCase(t.fulfilled,(y,{meta:n,payload:a})=>{let s=he(n.arg);R(y,n,a,s)}).addCase(t.rejected,(y,{meta:{condition:n,arg:a,requestId:s},error:f,payload:B})=>{Me(y,a.queryCacheKey,v=>{if(!n){if(v.requestId!==s)return;v.status="rejected",v.error=B??f}})}).addMatcher(w,(y,n)=>{let{queries:a}=h(n);for(let[s,f]of Object.entries(a))(f?.status==="fulfilled"||f?.status==="rejected")&&(y[s]=f)})}}),C=(0,o.createSlice)({name:`${e}/mutations`,initialState:Ce,reducers:{removeMutationResult:{reducer(d,{payload:y}){let n=se(y);n in d&&delete d[n]},prepare:(0,o.prepareAutoBatched)()}},extraReducers(d){d.addCase(p.pending,(y,{meta:n,meta:{requestId:a,arg:s,startedTimeStamp:f}})=>{s.track&&(y[se(n)]={requestId:a,status:"pending",endpointName:s.endpointName,startedTimeStamp:f})}).addCase(p.fulfilled,(y,{payload:n,meta:a})=>{a.arg.track&&ut(y,a,s=>{s.requestId===a.requestId&&(s.status="fulfilled",s.data=n,s.fulfilledTimeStamp=a.fulfilledTimeStamp)})}).addCase(p.rejected,(y,{payload:n,error:a,meta:s})=>{s.arg.track&&ut(y,s,f=>{f.requestId===s.requestId&&(f.status="rejected",f.error=n??a)})}).addMatcher(w,(y,n)=>{let{mutations:a}=h(n);for(let[s,f]of Object.entries(a))(f?.status==="fulfilled"||f?.status==="rejected")&&s!==f?.requestId&&(y[s]=f)})}}),P={tags:{},keys:{}},b=(0,o.createSlice)({name:`${e}/invalidation`,initialState:P,reducers:{updateProvidedBy:{reducer(d,y){for(let{queryCacheKey:n,providedTags:a}of y.payload){r(d,n);for(let{type:s,id:f}of a){let B=(d.tags[s]??={})[f||"__internal_without_id"]??=[];B.includes(n)||B.push(n)}d.keys[n]=a}},prepare:(0,o.prepareAutoBatched)()}},extraReducers(d){d.addCase(k.actions.removeQueryResult,(y,{payload:{queryCacheKey:n}})=>{r(y,n)}).addMatcher(w,(y,n)=>{let{provided:a}=h(n);for(let[s,f]of Object.entries(a.tags??{}))for(let[B,v]of Object.entries(f)){let U=(y.tags[s]??={})[B||"__internal_without_id"]??=[];for(let F of v)U.includes(F)||U.push(F),y.keys[F]=a.keys[F]}}).addMatcher((0,o.isAnyOf)((0,o.isFulfilled)(t),(0,o.isRejectedWithValue)(t)),(y,n)=>{m(y,[n])}).addMatcher(k.actions.cacheEntriesUpserted.match,(y,n)=>{let a=n.payload.map(({queryDescription:s,value:f})=>({type:"UNKNOWN",payload:f,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:s}}));m(y,a)})}});function r(d,y){let n=d.keys[y]??[];for(let a of n){let s=a.type,f=a.id??"__internal_without_id",B=d.tags[s]?.[f];B&&(d.tags[s][f]=B.filter(v=>v!==y))}delete d.keys[y]}function m(d,y){let n=y.map(a=>{let s=we(a,"providesTags",c,I),{queryCacheKey:f}=a.meta.arg;return{queryCacheKey:f,providedTags:s}});b.caseReducers.updateProvidedBy(d,b.actions.updateProvidedBy(n))}let i=(0,o.createSlice)({name:`${e}/subscriptions`,initialState:Ce,reducers:{updateSubscriptionOptions(d,y){},unsubscribeQueryResult(d,y){},internal_getRTKQSubscriptions(){}}}),u=(0,o.createSlice)({name:`${e}/internalSubscriptions`,initialState:Ce,reducers:{subscriptionsUpdated:{reducer(d,y){return(0,Ae.applyPatches)(d,y.payload)},prepare:(0,o.prepareAutoBatched)()}}}),l=(0,o.createSlice)({name:`${e}/config`,initialState:{online:We(),focused:ze(),middlewareRegistered:!1,...x},reducers:{middlewareRegistered(d,{payload:y}){d.middlewareRegistered=d.middlewareRegistered==="conflict"||A!==y?"conflict":!0}},extraReducers:d=>{d.addCase(ee,y=>{y.online=!0}).addCase(ce,y=>{y.online=!1}).addCase(Z,y=>{y.focused=!0}).addCase(de,y=>{y.focused=!1}).addMatcher(w,y=>({...y}))}}),T=(0,o.combineReducers)({queries:k.reducer,mutations:C.reducer,provided:b.reducer,subscriptions:u.reducer,config:l.reducer}),S=(d,y)=>T(E.match(y)?void 0:d,y),Q={...l.actions,...k.actions,...i.actions,...u.actions,...C.actions,...b.actions,resetApiState:E};return{reducer:S,actions:Q}}var Se=Symbol.for("RTKQ/skipToken"),lt={status:"uninitialized"},dt=(0,o.createNextState)(lt,()=>{}),ct=(0,o.createNextState)(lt,()=>{});function ft({serializeQueryArgs:e,reducerPath:t,createSelector:p}){let g=i=>dt,c=i=>ct;return{buildQuerySelector:R,buildInfiniteQuerySelector:k,buildMutationSelector:C,selectInvalidatedBy:P,selectCachedArgsForQuery:b,selectApiState:h,selectQueries:w,selectMutations:x,selectQueryEntry:I,selectConfig:E};function A(i){return{...i,...Ne(i.status)}}function h(i){return i[t]}function w(i){return h(i)?.queries}function I(i,u){return w(i)?.[u]}function x(i){return h(i)?.mutations}function E(i){return h(i)?.config}function D(i,u,l){return T=>{if(T===Se)return p(g,l);let S=e({queryArgs:T,endpointDefinition:u,endpointName:i});return p(d=>I(d,S)??dt,l)}}function R(i,u){return D(i,u,A)}function k(i,u){let{infiniteQueryOptions:l}=u;function T(S){let Q={...S,...Ne(S.status)},{isLoading:d,isError:y,direction:n}=Q,a=n==="forward",s=n==="backward";return{...Q,hasNextPage:r(l,Q.data,Q.originalArgs),hasPreviousPage:m(l,Q.data,Q.originalArgs),isFetchingNextPage:d&&a,isFetchingPreviousPage:d&&s,isFetchNextPageError:y&&a,isFetchPreviousPageError:y&&s}}return D(i,u,T)}function C(){return i=>{let u;return typeof i=="object"?u=se(i)??Se:u=i,p(u===Se?c:S=>h(S)?.mutations?.[u]??ct,A)}}function P(i,u){let l=i[t],T=new Set;for(let S of u.filter(re).map(Ie)){let Q=l.provided.tags[S.type];if(!Q)continue;let d=(S.id!==void 0?Q[S.id]:qe(Object.values(Q)))??[];for(let y of d)T.add(y)}return qe(Array.from(T.values()).map(S=>{let Q=l.queries[S];return Q?[{queryCacheKey:S,endpointName:Q.endpointName,originalArgs:Q.originalArgs}]:[]}))}function b(i,u){return Object.values(w(i)).filter(l=>l?.endpointName===u&&l.status!=="uninitialized").map(l=>l.originalArgs)}function r(i,u,l){return u?ke(i,u,l)!=null:!1}function m(i,u,l){return!u||!i.getPreviousPageParam?!1:Ue(i,u,l)!=null}}var gt=require("@reduxjs/toolkit");var mt=WeakMap?new WeakMap:void 0,xe=({endpointName:e,queryArgs:t})=>{let p="",g=mt?.get(t);if(typeof g=="string")p=g;else{let c=JSON.stringify(t,(A,h)=>(h=typeof h=="bigint"?{$bigint:h.toString()}:h,h=(0,o.isPlainObject)(h)?Object.keys(h).sort().reduce((w,I)=>(w[I]=h[I],w),{}):h,h));(0,o.isPlainObject)(t)&&mt?.set(t,c),p=c}return`${e}(${p})`};var Le=require("reselect");function Fe(...e){return function(p){let g=(0,Le.weakMapMemoize)(x=>p.extractRehydrationInfo?.(x,{reducerPath:p.reducerPath??"api"})),c={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...p,extractRehydrationInfo:g,serializeQueryArgs(x){let E=xe;if("serializeQueryArgs"in x.endpointDefinition){let D=x.endpointDefinition.serializeQueryArgs;E=R=>{let k=D(R);return typeof k=="string"?k:xe({...R,queryArgs:k})}}else p.serializeQueryArgs&&(E=p.serializeQueryArgs);return E(x)},tagTypes:[...p.tagTypes||[]]},A={endpointDefinitions:{},batch(x){x()},apiUid:(0,o.nanoid)(),extractRehydrationInfo:g,hasRehydrationInfo:(0,Le.weakMapMemoize)(x=>g(x)!=null)},h={injectEndpoints:I,enhanceEndpoints({addTagTypes:x,endpoints:E}){if(x)for(let D of x)c.tagTypes.includes(D)||c.tagTypes.push(D);if(E)for(let[D,R]of Object.entries(E))typeof R=="function"?R(A.endpointDefinitions[D]):Object.assign(A.endpointDefinitions[D]||{},R);return h}},w=e.map(x=>x.init(h,c,A));function I(x){let E=x.endpoints({query:D=>({...D,type:"query"}),mutation:D=>({...D,type:"mutation"}),infiniteQuery:D=>({...D,type:"infinitequery"})});for(let[D,R]of Object.entries(E)){if(x.overrideExisting!==!0&&D in A.endpointDefinitions){if(x.overrideExisting==="throw")throw new Error((0,gt.formatProdErrorMessage)(39));typeof process<"u";continue}typeof process<"u",A.endpointDefinitions[D]=R;for(let k of w)k.injectEndpoint(D,R)}return h}return h.injectEndpoints({endpoints:p.endpoints})}}var Qt=require("@reduxjs/toolkit"),Tt=Symbol();function ht(){return function(){throw new Error((0,Qt.formatProdErrorMessage)(33))}}var wt=require("immer");function G(e,...t){return Object.assign(e,...t)}var Rt=require("immer");var At=({api:e,queryThunk:t,internalState:p,mwApi:g})=>{let c=`${e.reducerPath}/subscriptions`,A=null,h=null,{updateSubscriptionOptions:w,unsubscribeQueryResult:I}=e.internalActions,x=(P,b)=>{if(w.match(b)){let{queryCacheKey:m,requestId:i,options:u}=b.payload,l=P.get(m);return l?.has(i)&&l.set(i,u),!0}if(I.match(b)){let{queryCacheKey:m,requestId:i}=b.payload,u=P.get(m);return u&&u.delete(i),!0}if(e.internalActions.removeQueryResult.match(b))return P.delete(b.payload.queryCacheKey),!0;if(t.pending.match(b)){let{meta:{arg:m,requestId:i}}=b,u=ge(P,m.queryCacheKey,Qe);return m.subscribe&&u.set(i,m.subscriptionOptions??u.get(i)??{}),!0}let r=!1;if(t.rejected.match(b)){let{meta:{condition:m,arg:i,requestId:u}}=b;if(m&&i.subscribe){let l=ge(P,i.queryCacheKey,Qe);l.set(u,i.subscriptionOptions??l.get(u)??{}),r=!0}}return r},E=()=>p.currentSubscriptions,k={getSubscriptions:E,getSubscriptionCount:P=>E().get(P)?.size??0,isRequestSubscribed:(P,b)=>!!E()?.get(P)?.get(b)};function C(P){return JSON.parse(JSON.stringify(Object.fromEntries([...P].map(([b,r])=>[b,Object.fromEntries(r)]))))}return(P,b)=>{if(A||(A=C(p.currentSubscriptions)),e.util.resetApiState.match(P))return A={},p.currentSubscriptions.clear(),h=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(P))return[!1,k];let r=x(p.currentSubscriptions,P),m=!0;if(r){h||(h=setTimeout(()=>{let l=C(p.currentSubscriptions),[,T]=(0,Rt.produceWithPatches)(A,()=>l);b.next(e.internalActions.subscriptionsUpdated(T)),A=l,h=null},500));let i=typeof P.type=="string"&&!!P.type.startsWith(c),u=t.rejected.match(P)&&P.meta.condition&&!!P.meta.arg.subscribe;m=!i&&!u}return[m,!1]}};var $t=2147483647/1e3-1,St=({reducerPath:e,api:t,queryThunk:p,context:g,internalState:c,selectors:{selectQueryEntry:A,selectConfig:h},getRunningQueryThunk:w,mwApi:I})=>{let{removeQueryResult:x,unsubscribeQueryResult:E,cacheEntriesUpserted:D}=t.internalActions,R=c.runningQueries.get(I.dispatch),k=(0,o.isAnyOf)(E.match,p.fulfilled,p.rejected,D.match);function C(i){let u=c.currentSubscriptions.get(i);if(!u)return!1;let l=u.size>0,T=R?.[i]!==void 0;return l||T}let P={},b=(i,u,l)=>{let T=u.getState(),S=h(T);if(k(i)){let Q;if(D.match(i))Q=i.payload.map(d=>d.queryDescription.queryCacheKey);else{let{queryCacheKey:d}=E.match(i)?i.payload:i.meta.arg;Q=[d]}r(Q,u,S)}if(t.util.resetApiState.match(i))for(let[Q,d]of Object.entries(P))d&&clearTimeout(d),delete P[Q];if(g.hasRehydrationInfo(i)){let{queries:Q}=g.extractRehydrationInfo(i);r(Object.keys(Q),u,S)}};function r(i,u,l){let T=u.getState();for(let S of i){let Q=A(T,S);Q?.endpointName&&m(S,Q.endpointName,u,l)}}function m(i,u,l,T){let Q=g.endpointDefinitions[u]?.keepUnusedDataFor??T.keepUnusedDataFor;if(Q===1/0)return;let d=Math.max(0,Math.min(Q,$t));if(!C(i)){let y=P[i];y&&clearTimeout(y),P[i]=setTimeout(()=>{if(!C(i)){let n=A(l.getState(),i);n?.endpointName&&l.dispatch(w(n.endpointName,n.originalArgs))?.abort(),l.dispatch(x({queryCacheKey:i}))}delete P[i]},d*1e3)}}return b};var xt=new Error("Promise never resolved before cacheEntryRemoved."),Dt=({api:e,reducerPath:t,context:p,queryThunk:g,mutationThunk:c,internalState:A,selectors:{selectQueryEntry:h,selectApiState:w}})=>{let I=(0,o.isAsyncThunkAction)(g),x=(0,o.isAsyncThunkAction)(c),E=(0,o.isFulfilled)(g,c),D={};function R(r,m,i){let u=D[r];u?.valueResolved&&(u.valueResolved({data:m,meta:i}),delete u.valueResolved)}function k(r){let m=D[r];m&&(delete D[r],m.cacheEntryRemoved())}let C=(r,m,i)=>{let u=P(r);function l(T,S,Q,d){let y=h(i,S),n=h(m.getState(),S);!y&&n&&b(T,d,S,m,Q)}if(g.pending.match(r))l(r.meta.arg.endpointName,u,r.meta.requestId,r.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(r))for(let{queryDescription:T,value:S}of r.payload){let{endpointName:Q,originalArgs:d,queryCacheKey:y}=T;l(Q,y,r.meta.requestId,d),R(y,S,{})}else if(c.pending.match(r))m.getState()[t].mutations[u]&&b(r.meta.arg.endpointName,r.meta.arg.originalArgs,u,m,r.meta.requestId);else if(E(r))R(u,r.payload,r.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(r)||e.internalActions.removeMutationResult.match(r))k(u);else if(e.util.resetApiState.match(r))for(let T of Object.keys(D))k(T)};function P(r){return I(r)?r.meta.arg.queryCacheKey:x(r)?r.meta.arg.fixedCacheKey??r.meta.requestId:e.internalActions.removeQueryResult.match(r)?r.payload.queryCacheKey:e.internalActions.removeMutationResult.match(r)?se(r.payload):""}function b(r,m,i,u,l){let T=p.endpointDefinitions[r],S=T?.onCacheEntryAdded;if(!S)return;let Q={},d=new Promise(B=>{Q.cacheEntryRemoved=B}),y=Promise.race([new Promise(B=>{Q.valueResolved=B}),d.then(()=>{throw xt})]);y.catch(()=>{}),D[i]=Q;let n=e.endpoints[r].select(le(T)?m:i),a=u.dispatch((B,v,U)=>U),s={...u,getCacheEntry:()=>n(u.getState()),requestId:l,extra:a,updateCachedData:le(T)?B=>u.dispatch(e.util.updateQueryData(r,m,B)):void 0,cacheDataLoaded:y,cacheEntryRemoved:d},f=S(m,s);Promise.resolve(f).catch(B=>{if(B!==xt)throw B})}return C};var bt=({api:e,context:{apiUid:t},reducerPath:p})=>(g,c)=>{e.util.resetApiState.match(g)&&c.dispatch(e.internalActions.middlewareRegistered(t)),typeof process<"u"};var Et=({reducerPath:e,context:t,context:{endpointDefinitions:p},mutationThunk:g,queryThunk:c,api:A,assertTagType:h,refetchQuery:w,internalState:I})=>{let{removeQueryResult:x}=A.internalActions,E=(0,o.isAnyOf)((0,o.isFulfilled)(g),(0,o.isRejectedWithValue)(g)),D=(0,o.isAnyOf)((0,o.isFulfilled)(g,c),(0,o.isRejected)(g,c)),R=[],k=(b,r)=>{E(b)?P(we(b,"invalidatesTags",p,h),r):D(b)?P([],r):A.util.invalidateTags.match(b)&&P(Te(b.payload,void 0,void 0,void 0,void 0,h),r)};function C(b){let{queries:r,mutations:m}=b;for(let i of[r,m])for(let u in i)if(i[u]?.status==="pending")return!0;return!1}function P(b,r){let m=r.getState(),i=m[e];if(R.push(...b),i.config.invalidationBehavior==="delayed"&&C(i))return;let u=R;if(R=[],u.length===0)return;let l=A.util.selectInvalidatedBy(m,u);t.batch(()=>{let T=Array.from(l.values());for(let{queryCacheKey:S}of T){let Q=i.queries[S],d=ge(I.currentSubscriptions,S,Qe);Q&&(d.size===0?r.dispatch(x({queryCacheKey:S})):Q.status!=="uninitialized"&&r.dispatch(w(Q)))}})}return k};var Pt=({reducerPath:e,queryThunk:t,api:p,refetchQuery:g,internalState:c})=>{let{currentPolls:A,currentSubscriptions:h}=c,w=new Set,I=null,x=(r,m)=>{(p.internalActions.updateSubscriptionOptions.match(r)||p.internalActions.unsubscribeQueryResult.match(r))&&E(r.payload.queryCacheKey,m),(t.pending.match(r)||t.rejected.match(r)&&r.meta.condition)&&E(r.meta.arg.queryCacheKey,m),(t.fulfilled.match(r)||t.rejected.match(r)&&!r.meta.condition)&&R(r.meta.arg,m),p.util.resetApiState.match(r)&&(P(),I&&(clearTimeout(I),I=null),w.clear())};function E(r,m){w.add(r),I||(I=setTimeout(()=>{for(let i of w)k({queryCacheKey:i},m);w.clear(),I=null},0))}function D(r,m){let u=m.getState()[e].queries[r],l=h.get(r);if(!(!u||u.status==="uninitialized"))return l}function R({queryCacheKey:r},m){let i=m.getState()[e],u=i.queries[r],l=h.get(r);if(!u||u.status==="uninitialized")return;let{lowestPollingInterval:T,skipPollingIfUnfocused:S}=b(l);if(!Number.isFinite(T))return;let Q=A.get(r);Q?.timeout&&(clearTimeout(Q.timeout),Q.timeout=void 0);let d=Date.now()+T;A.set(r,{nextPollTimestamp:d,pollingInterval:T,timeout:setTimeout(()=>{(i.config.focused||!S)&&m.dispatch(g(u)),R({queryCacheKey:r},m)},T)})}function k({queryCacheKey:r},m){let u=m.getState()[e].queries[r],l=h.get(r);if(!u||u.status==="uninitialized")return;let{lowestPollingInterval:T}=b(l);if(!Number.isFinite(T)){C(r);return}let S=A.get(r),Q=Date.now()+T;(!S||Q<S.nextPollTimestamp)&&R({queryCacheKey:r},m)}function C(r){let m=A.get(r);m?.timeout&&clearTimeout(m.timeout),A.delete(r)}function P(){for(let r of A.keys())C(r)}function b(r=new Map){let m=!1,i=Number.POSITIVE_INFINITY;for(let u of r.values())u.pollingInterval&&(i=Math.min(u.pollingInterval,i),m=u.skipPollingIfUnfocused||m);return{lowestPollingInterval:i,skipPollingIfUnfocused:m}}return x};var It=({api:e,context:t,queryThunk:p,mutationThunk:g})=>{let c=(0,o.isPending)(p,g),A=(0,o.isRejected)(p,g),h=(0,o.isFulfilled)(p,g),w={};return(x,E)=>{if(c(x)){let{requestId:D,arg:{endpointName:R,originalArgs:k}}=x.meta,C=t.endpointDefinitions[R],P=C?.onQueryStarted;if(P){let b={},r=new Promise((l,T)=>{b.resolve=l,b.reject=T});r.catch(()=>{}),w[D]=b;let m=e.endpoints[R].select(le(C)?k:D),i=E.dispatch((l,T,S)=>S),u={...E,getCacheEntry:()=>m(E.getState()),requestId:D,extra:i,updateCachedData:le(C)?l=>E.dispatch(e.util.updateQueryData(R,k,l)):void 0,queryFulfilled:r};P(k,u)}}else if(h(x)){let{requestId:D,baseQueryMeta:R}=x.meta;w[D]?.resolve({data:x.payload,meta:R}),delete w[D]}else if(A(x)){let{requestId:D,rejectedWithValue:R,baseQueryMeta:k}=x.meta;w[D]?.reject({error:x.payload??x.error,isUnhandledError:!R,meta:k}),delete w[D]}}};var kt=({reducerPath:e,context:t,api:p,refetchQuery:g,internalState:c})=>{let{removeQueryResult:A}=p.internalActions,h=(I,x)=>{Z.match(I)&&w(x,"refetchOnFocus"),ee.match(I)&&w(x,"refetchOnReconnect")};function w(I,x){let E=I.getState()[e],D=E.queries,R=c.currentSubscriptions;t.batch(()=>{for(let k of R.keys()){let C=D[k],P=R.get(k);if(!P||!C)continue;let b=[...P.values()];(b.some(m=>m[x]===!0)||b.every(m=>m[x]===void 0)&&E.config[x])&&(P.size===0?I.dispatch(A({queryCacheKey:k})):C.status!=="uninitialized"&&I.dispatch(g(C)))}})}return h};function Bt(e){let{reducerPath:t,queryThunk:p,api:g,context:c,internalState:A}=e,{apiUid:h}=c,w={invalidateTags:(0,o.createAction)(`${t}/invalidateTags`)},I=R=>R.type.startsWith(`${t}/`),x=[bt,St,Et,Pt,Dt,It];return{middleware:R=>{let k=!1,C={...e,internalState:A,refetchQuery:D,isThisApiSliceAction:I,mwApi:R},P=x.map(m=>m(C)),b=At(C),r=kt(C);return m=>i=>{if(!(0,o.isAction)(i))return m(i);k||(k=!0,R.dispatch(g.internalActions.middlewareRegistered(h)));let u={...R,next:m},l=R.getState(),[T,S]=b(i,u,l),Q;if(T?Q=m(i):Q=S,R.getState()[t]&&(r(i,u,l),I(i)||c.hasRehydrationInfo(i)))for(let d of P)d(i,u,l);return Q}},actions:w};function D(R){return e.api.endpoints[R.endpointName].initiate(R.originalArgs,{subscribe:!1,forceRefetch:!0})}}var De=Symbol(),ve=({createSelector:e=o.createSelector}={})=>({name:De,init(t,{baseQuery:p,tagTypes:g,reducerPath:c,serializeQueryArgs:A,keepUnusedDataFor:h,refetchOnMountOrArgChange:w,refetchOnFocus:I,refetchOnReconnect:x,invalidationBehavior:E,onSchemaFailure:D,catchSchemaFailure:R,skipSchemaValidation:k},C){(0,wt.enablePatches)();let P=H=>(typeof process<"u",H);Object.assign(t,{reducerPath:c,endpoints:{},internalActions:{onOnline:ee,onOffline:ce,onFocus:Z,onFocusLost:de},util:{}});let b=ft({serializeQueryArgs:A,reducerPath:c,createSelector:e}),{selectInvalidatedBy:r,selectCachedArgsForQuery:m,buildQuerySelector:i,buildInfiniteQuerySelector:u,buildMutationSelector:l}=b;G(t.util,{selectInvalidatedBy:r,selectCachedArgsForQuery:m});let{queryThunk:T,infiniteQueryThunk:S,mutationThunk:Q,patchQueryData:d,updateQueryData:y,upsertQueryData:n,prefetch:a,buildMatchThunkActions:s}=st({baseQuery:p,reducerPath:c,context:C,api:t,serializeQueryArgs:A,assertTagType:P,selectors:b,onSchemaFailure:D,catchSchemaFailure:R,skipSchemaValidation:k}),{reducer:f,actions:B}=pt({context:C,queryThunk:T,infiniteQueryThunk:S,mutationThunk:Q,serializeQueryArgs:A,reducerPath:c,assertTagType:P,config:{refetchOnFocus:I,refetchOnReconnect:x,refetchOnMountOrArgChange:w,keepUnusedDataFor:h,reducerPath:c,invalidationBehavior:E}});G(t.util,{patchQueryData:d,updateQueryData:y,upsertQueryData:n,prefetch:a,resetApiState:B.resetApiState,upsertQueryEntries:B.cacheEntriesUpserted}),G(t.internalActions,B);let v={currentSubscriptions:new Map,currentPolls:new Map,runningQueries:new Map,runningMutations:new Map},{buildInitiateQuery:U,buildInitiateInfiniteQuery:F,buildInitiateMutation:M,getRunningMutationThunk:N,getRunningMutationsThunk:V,getRunningQueriesThunk:z,getRunningQueryThunk:j}=it({queryThunk:T,mutationThunk:Q,infiniteQueryThunk:S,api:t,serializeQueryArgs:A,context:C,internalState:v});G(t.util,{getRunningMutationThunk:N,getRunningMutationsThunk:V,getRunningQueryThunk:j,getRunningQueriesThunk:z});let{middleware:K,actions:L}=Bt({reducerPath:c,context:C,queryThunk:T,mutationThunk:Q,infiniteQueryThunk:S,api:t,assertTagType:P,selectors:b,getRunningQueryThunk:j,internalState:v});return G(t.util,L),G(t,{reducer:f,middleware:K}),{name:De,injectEndpoint(H,_){let O=t,q=O.endpoints[H]??={};ie(_)&&G(q,{name:H,select:i(H,_),initiate:U(H,_)},s(T,H)),nt(_)&&G(q,{name:H,select:l(),initiate:M(H)},s(Q,H)),ae(_)&&G(q,{name:H,select:u(H,_),initiate:F(H,_)},s(T,H))}}}});var Mt=Fe(ve());0&&(module.exports={NamedSchemaError,QueryStatus,_NEVER,buildCreateApi,copyWithStructuralSharing,coreModule,coreModuleName,createApi,defaultSerializeQueryArgs,fakeBaseQuery,fetchBaseQuery,retry,setupListeners,skipToken});
//# sourceMappingURL=rtk-query.production.min.cjs.map