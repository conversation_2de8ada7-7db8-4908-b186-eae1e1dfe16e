{"version": 3, "sources": ["../src/utils/env.ts", "../src/utils/errors.ts", "../src/utils/common.ts", "../src/utils/plugins.ts", "../src/core/scope.ts", "../src/core/finalize.ts", "../src/core/proxy.ts", "../src/core/immerClass.ts", "../src/core/current.ts", "../src/plugins/patches.ts", "../src/plugins/mapset.ts", "../src/immer.ts"], "sourcesContent": ["// Should be no imports here!\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: unique symbol = Symbol.for(\"immer-nothing\")\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = Symbol.for(\"immer-draftable\")\n\nexport const DRAFT_STATE: unique symbol = Symbol.for(\"immer-state\")\n", "export const errors =\n\tprocess.env.NODE_ENV !== \"production\"\n\t\t? [\n\t\t\t\t// All error codes, starting by 0:\n\t\t\t\tfunction(plugin: string) {\n\t\t\t\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t\t\t\t},\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t\t\t\t},\n\t\t\t\t\"This object has been frozen and should not be mutated\",\n\t\t\t\tfunction(data: any) {\n\t\t\t\t\treturn (\n\t\t\t\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\t\t\t\tdata\n\t\t\t\t\t)\n\t\t\t\t},\n\t\t\t\t\"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t\t\t\t\"Immer forbids circular references\",\n\t\t\t\t\"The first or second argument to `produce` must be a function\",\n\t\t\t\t\"The third argument to `produce` must be a function or undefined\",\n\t\t\t\t\"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t\t\t\t\"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `'current' expects a draft, got: ${thing}`\n\t\t\t\t},\n\t\t\t\t\"Object.defineProperty() cannot be used on an Immer draft\",\n\t\t\t\t\"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t\t\t\t\"Immer only supports deleting array indices\",\n\t\t\t\t\"Immer only supports setting array indices and the 'length' property\",\n\t\t\t\tfunction(thing: string) {\n\t\t\t\t\treturn `'original' expects a draft, got: ${thing}`\n\t\t\t\t}\n\t\t\t\t// Note: if more errors are added, the errorOffset in Patches.ts should be increased\n\t\t\t\t// See Patches.ts for additional errors\n\t\t  ]\n\t\t: []\n\nexport function die(error: number, ...args: any[]): never {\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\tconst e = errors[error]\n\t\tconst msg = typeof e === \"function\" ? e.apply(null, args as any) : e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}. Full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\tArchType,\n\tdie,\n\tStrictMode\n} from \"../internal\"\n\nexport const getPrototypeOf = Object.getPrototypeOf\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(15, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/**\n * Each iterates a map, set or array.\n * Or, if any other kind of object, all of its own properties.\n * Regardless whether they are enumerable or symbols\n */\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void\n): void\nexport function each(obj: any, iter: any) {\n\tif (getArchtype(obj) === ArchType.Object) {\n\t\tReflect.ownKeys(obj).forEach(key => {\n\t\t\titer(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): ArchType {\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_\n\t\t: Array.isArray(thing)\n\t\t? ArchType.Array\n\t\t: isMap(thing)\n\t\t? ArchType.Map\n\t\t: isSet(thing)\n\t\t? ArchType.Set\n\t\t: ArchType.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === ArchType.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === ArchType.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === ArchType.Map) thing.set(propOrOldValue, value)\n\telse if (t === ArchType.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any, strict: StrictMode) {\n\tif (isMap(base)) {\n\t\treturn new Map(base)\n\t}\n\tif (isSet(base)) {\n\t\treturn new Set(base)\n\t}\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\n\tconst isPlain = isPlainObject(base)\n\n\tif (strict === true || (strict === \"class_only\" && !isPlain)) {\n\t\t// Perform a strict copy\n\t\tconst descriptors = Object.getOwnPropertyDescriptors(base)\n\t\tdelete descriptors[DRAFT_STATE as any]\n\t\tlet keys = Reflect.ownKeys(descriptors)\n\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\tconst key: any = keys[i]\n\t\t\tconst desc = descriptors[key]\n\t\t\tif (desc.writable === false) {\n\t\t\t\tdesc.writable = true\n\t\t\t\tdesc.configurable = true\n\t\t\t}\n\t\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t\t// with libraries that trap values, like mobx or vue\n\t\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\t\tif (desc.get || desc.set)\n\t\t\t\tdescriptors[key] = {\n\t\t\t\t\tconfigurable: true,\n\t\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\t\tvalue: base[key]\n\t\t\t\t}\n\t\t}\n\t\treturn Object.create(getPrototypeOf(base), descriptors)\n\t} else {\n\t\t// perform a sloppy copy\n\t\tconst proto = getPrototypeOf(base)\n\t\tif (proto !== null && isPlain) {\n\t\t\treturn {...base} // assumption: better inner class optimization than the assign below\n\t\t}\n\t\tconst obj = Object.create(proto)\n\t\treturn Object.assign(obj, base)\n\t}\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\t Object.defineProperties(obj, {\n                        set: {value: dontMutateFrozenCollections as any},\n                        add: {value: dontMutateFrozenCollections as any},\n                        clear: {value: dontMutateFrozenCollections as any},\n                        delete: {value: dontMutateFrozenCollections as any}\n                })\n\t}\n\tObject.freeze(obj)\n\tif (deep)\n\t\t// See #590, don't recurse into non-enumerable / Symbol properties when freezing\n\t\t// So use Object.values (only string-like, enumerables) instead of each()\n\t\tObject.values(obj).forEach(value => freeze(value, true))\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\tDrafted,\n\tImmerB<PERSON>State,\n\tAnyMap,\n\tAnySet,\n\tArchType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: readonly Patch[]): T\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(0, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ArchType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ArchType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\t<PERSON>L<PERSON>ener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tArchType,\n\tgetPlugin\n} from \"../internal\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (state.type_ === ArchType.Object || state.type_ === ArchType.Array)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tImmerScope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchPath,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tArchType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen,\n\tisMap\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(value, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path)\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result = state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ArchType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (process.env.NODE_ENV !== \"production\" && childValue === targetObject)\n\t\tdie(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ArchType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// Immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\t// Per #590, we never freeze symbolic properties. Just to make sure don't accidentally interfere\n\t\t// with other frameworks.\n\t\tif (\n\t\t\t(!parentState || !parentState.scope_.parent_) &&\n\t\t\ttypeof prop !== \"symbol\" &&\n\t\t\t(isMap(targetObject)\n\t\t\t\t? targetObject.has(prop)\n\t\t\t\t: Object.prototype.propertyIsEnumerable.call(targetObject, prop))\n\t\t)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tgetPrototypeOf,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tArchType,\n\tImmerScope\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ArchType.Object\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ArchType.Array\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ArchType.Array : (ArchType.Object as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(value, state))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\tif (state.copy_) {\n\t\t\tdelete state.copy_[prop]\n\t\t}\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ArchType.Array || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (process.env.NODE_ENV !== \"production\" && isNaN(parseInt(prop as any)))\n\t\tdie(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (\n\t\tprocess.env.NODE_ENV !== \"production\" &&\n\t\tprop !== \"length\" &&\n\t\tisNaN(parseInt(prop as any))\n\t)\n\t\tdie(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {\n\tbase_: any\n\tcopy_: any\n\tscope_: ImmerScope\n}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(\n\t\t\tstate.base_,\n\t\t\tstate.scope_.immer_.useStrictShallowCopy_\n\t\t)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessResult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport type StrictMode = boolean | \"class_only\";\n\nexport class Immer implements ProducersFns {\n\tautoFreeze_: boolean = true\n\tuseStrictShallowCopy_: StrictMode = false\n\n\tconstructor(config?: {\n\t\tautoFreeze?: boolean\n\t\tuseStrictShallowCopy?: StrictMode\n\t}) {\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t\tif (typeof config?.useStrictShallowCopy === \"boolean\")\n\t\t\tthis.setUseStrictShallowCopy(config!.useStrictShallowCopy)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(1, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (!state || !state.isManual_) die(9)\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to enable strict shallow copy.\n\t *\n\t * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n\t */\n\tsetUseStrictShallowCopy(value: StrictMode) {\n\t\tthis.useStrictShallowCopy_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: readonly Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: createProxyProxy(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tisFrozen\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(10, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value) || isFrozen(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tif (state) {\n\t\tif (!state.modified_) return state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = shallowCopy(value, state.scope_.immer_.useStrictShallowCopy_)\n\t} else {\n\t\tcopy = shallowCopy(value, true)\n\t}\n\t// recurse\n\teach(copy, (key, childValue) => {\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\tif (state) {\n\t\tstate.finalized_ = false\n\t}\n\treturn copy\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tProxyArrayState,\n\tMapState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tgetPrototypeOf,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tArchType,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING,\n\terrors\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst errorOffset = 16\n\tif (process.env.NODE_ENV !== \"production\") {\n\t\terrors.push(\n\t\t\t'Sets cannot have \"replace\" patches.',\n\t\t\tfunction(op: string) {\n\t\t\t\treturn \"Unsupported patch operation: \" + op\n\t\t\t},\n\t\t\tfunction(path: string) {\n\t\t\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t\t\t},\n\t\t\t\"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n\t\t)\n\t}\n\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ArchType.Object:\n\t\t\tcase ArchType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ArchType.Array:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ArchType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tfor (let i = copy_.length - 1; base_.length <= i; --i) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tinversePatches.push({\n\t\t\t\top: REMOVE,\n\t\t\t\tpath\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: readonly Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === ArchType.Object || parentType === ArchType.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(errorOffset + 3)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\")\n\t\t\t\t\tdie(errorOffset + 3)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(errorOffset + 2, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\tdie(errorOffset)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase ArchType.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase ArchType.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase ArchType.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(errorOffset + 1, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tdie,\n\tArchType,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\tclass DraftMap extends Map {\n\t\t[DRAFT_STATE]: MapState\n\n\t\tconstructor(target: AnyMap, parent?: ImmerState) {\n\t\t\tsuper()\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ArchType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t}\n\t\t}\n\n\t\tget size(): number {\n\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t}\n\n\t\thas(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tset(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tdelete(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tclear() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tforEach(cb: (value: any, key: any, self: any) => void, thisArg?: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tget(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tkeys(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tvalues(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[Symbol.iterator]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tentries(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[Symbol.iterator]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\t[Symbol.iterator]() {\n\t\t\treturn this.entries()\n\t\t}\n\t}\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tclass DraftSet extends Set {\n\t\t[DRAFT_STATE]: SetState\n\t\tconstructor(target: AnySet, parent?: ImmerState) {\n\t\t\tsuper()\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ArchType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t}\n\t\t}\n\n\t\tget size(): number {\n\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t}\n\n\t\thas(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tadd(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tdelete(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tclear() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tvalues(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tentries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tkeys(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\t[Symbol.iterator]() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tforEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\t}\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tWritableDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\tProducer,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze,\n\tObjectish,\n\tStrictMode\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = /* @__PURE__ */ immer.produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = /* @__PURE__ */ immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = /* @__PURE__ */ immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to enable strict shallow copy.\n *\n * By default, immer does not copy the object descriptors such as getter, setter and non-enumrable properties.\n */\nexport const setUseStrictShallowCopy = /* @__PURE__ */ immer.setUseStrictShallowCopy.bind(\n\timmer\n)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = /* @__PURE__ */ immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = /* @__PURE__ */ immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = /* @__PURE__ */ immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAKO,IAAM,UAAyB,OAAO,IAAI,eAAe;AAUzD,IAAM,YAA2B,OAAO,IAAI,iBAAiB;AAE7D,IAAM,cAA6B,OAAO,IAAI,aAAa;;;ACjB3D,IAAM,SACZ,QAAQ,IAAI,aAAa,eACtB;AAAA;AAAA,EAEA,SAAS,QAAgB;AACxB,WAAO,mBAAmB,yFAAyF;AAAA,EACpH;AAAA,EACA,SAAS,OAAe;AACvB,WAAO,sJAAsJ;AAAA,EAC9J;AAAA,EACA;AAAA,EACA,SAAS,MAAW;AACnB,WACC,yHACA;AAAA,EAEF;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,OAAe;AACvB,WAAO,mCAAmC;AAAA,EAC3C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,OAAe;AACvB,WAAO,oCAAoC;AAAA,EAC5C;AAAA;AAAA;AAGA,IACA,CAAC;AAEE,SAAS,IAAI,UAAkB,MAAoB;AACzD,MAAI,QAAQ,IAAI,aAAa,cAAc;AAC1C,UAAM,IAAI,OAAO,KAAK;AACtB,UAAM,MAAM,OAAO,MAAM,aAAa,EAAE,MAAM,MAAM,IAAW,IAAI;AACnE,UAAM,IAAI,MAAM,WAAW,KAAK;AAAA,EACjC;AACA,QAAM,IAAI;AAAA,IACT,8BAA8B;AAAA,EAC/B;AACD;;;ACjCO,IAAM,iBAAiB,OAAO;AAI9B,SAAS,QAAQ,OAAqB;AAC5C,SAAO,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,WAAW;AACtC;AAIO,SAAS,YAAY,OAAqB;AAxBjD;AAyBC,MAAI,CAAC;AAAO,WAAO;AACnB,SACC,cAAc,KAAK,KACnB,MAAM,QAAQ,KAAK,KACnB,CAAC,CAAC,MAAM,SAAS,KACjB,CAAC,GAAC,WAAM,gBAAN,mBAAoB,eACtB,MAAM,KAAK,KACX,MAAM,KAAK;AAEb;AAEA,IAAM,mBAAmB,OAAO,UAAU,YAAY,SAAS;AAExD,SAAS,cAAc,OAAqB;AAClD,MAAI,CAAC,SAAS,OAAO,UAAU;AAAU,WAAO;AAChD,QAAM,QAAQ,eAAe,KAAK;AAClC,MAAI,UAAU,MAAM;AACnB,WAAO;AAAA,EACR;AACA,QAAM,OACL,OAAO,eAAe,KAAK,OAAO,aAAa,KAAK,MAAM;AAE3D,MAAI,SAAS;AAAQ,WAAO;AAE5B,SACC,OAAO,QAAQ,cACf,SAAS,SAAS,KAAK,IAAI,MAAM;AAEnC;AAKO,SAAS,SAAS,OAA0B;AAClD,MAAI,CAAC,QAAQ,KAAK;AAAG,QAAI,IAAI,KAAK;AAClC,SAAO,MAAM,WAAW,EAAE;AAC3B;AAWO,SAAS,KAAK,KAAU,MAAW;AACzC,MAAI,YAAY,GAAG,sBAAuB;AACzC,YAAQ,QAAQ,GAAG,EAAE,QAAQ,SAAO;AACnC,WAAK,KAAK,IAAI,GAAG,GAAG,GAAG;AAAA,IACxB,CAAC;AAAA,EACF,OAAO;AACN,QAAI,QAAQ,CAAC,OAAY,UAAe,KAAK,OAAO,OAAO,GAAG,CAAC;AAAA,EAChE;AACD;AAGO,SAAS,YAAY,OAAsB;AACjD,QAAM,QAAgC,MAAM,WAAW;AACvD,SAAO,QACJ,MAAM,QACN,MAAM,QAAQ,KAAK,oBAEnB,MAAM,KAAK,kBAEX,MAAM,KAAK;AAGf;AAGO,SAAS,IAAI,OAAY,MAA4B;AAC3D,SAAO,YAAY,KAAK,oBACrB,MAAM,IAAI,IAAI,IACd,OAAO,UAAU,eAAe,KAAK,OAAO,IAAI;AACpD;AAGO,SAAS,IAAI,OAA2B,MAAwB;AAEtE,SAAO,YAAY,KAAK,oBAAqB,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI;AAC1E;AAGO,SAAS,IAAI,OAAY,gBAA6B,OAAY;AACxE,QAAM,IAAI,YAAY,KAAK;AAC3B,MAAI;AAAoB,UAAM,IAAI,gBAAgB,KAAK;AAAA,WAC9C,mBAAoB;AAC5B,UAAM,IAAI,KAAK;AAAA,EAChB;AAAO,UAAM,cAAc,IAAI;AAChC;AAGO,SAAS,GAAG,GAAQ,GAAiB;AAE3C,MAAI,MAAM,GAAG;AACZ,WAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,EACjC,OAAO;AACN,WAAO,MAAM,KAAK,MAAM;AAAA,EACzB;AACD;AAGO,SAAS,MAAM,QAA+B;AACpD,SAAO,kBAAkB;AAC1B;AAGO,SAAS,MAAM,QAA+B;AACpD,SAAO,kBAAkB;AAC1B;AAEO,SAAS,OAAO,OAAwB;AAC9C,SAAO,MAAM,SAAS,MAAM;AAC7B;AAGO,SAAS,YAAY,MAAW,QAAoB;AAC1D,MAAI,MAAM,IAAI,GAAG;AAChB,WAAO,IAAI,IAAI,IAAI;AAAA,EACpB;AACA,MAAI,MAAM,IAAI,GAAG;AAChB,WAAO,IAAI,IAAI,IAAI;AAAA,EACpB;AACA,MAAI,MAAM,QAAQ,IAAI;AAAG,WAAO,MAAM,UAAU,MAAM,KAAK,IAAI;AAE/D,QAAM,UAAU,cAAc,IAAI;AAElC,MAAI,WAAW,QAAS,WAAW,gBAAgB,CAAC,SAAU;AAE7D,UAAM,cAAc,OAAO,0BAA0B,IAAI;AACzD,WAAO,YAAY,WAAkB;AACrC,QAAI,OAAO,QAAQ,QAAQ,WAAW;AACtC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,YAAM,MAAW,KAAK,CAAC;AACvB,YAAM,OAAO,YAAY,GAAG;AAC5B,UAAI,KAAK,aAAa,OAAO;AAC5B,aAAK,WAAW;AAChB,aAAK,eAAe;AAAA,MACrB;AAIA,UAAI,KAAK,OAAO,KAAK;AACpB,oBAAY,GAAG,IAAI;AAAA,UAClB,cAAc;AAAA,UACd,UAAU;AAAA;AAAA,UACV,YAAY,KAAK;AAAA,UACjB,OAAO,KAAK,GAAG;AAAA,QAChB;AAAA,IACF;AACA,WAAO,OAAO,OAAO,eAAe,IAAI,GAAG,WAAW;AAAA,EACvD,OAAO;AAEN,UAAM,QAAQ,eAAe,IAAI;AACjC,QAAI,UAAU,QAAQ,SAAS;AAC9B,aAAO,mBAAI;AAAA,IACZ;AACA,UAAM,MAAM,OAAO,OAAO,KAAK;AAC/B,WAAO,OAAO,OAAO,KAAK,IAAI;AAAA,EAC/B;AACD;AAUO,SAAS,OAAU,KAAU,OAAgB,OAAU;AAC7D,MAAI,SAAS,GAAG,KAAK,QAAQ,GAAG,KAAK,CAAC,YAAY,GAAG;AAAG,WAAO;AAC/D,MAAI,YAAY,GAAG,IAAI,GAAoB;AACzC,WAAO,iBAAiB,KAAK;AAAA,MACR,KAAK,EAAC,OAAO,4BAAkC;AAAA,MAC/C,KAAK,EAAC,OAAO,4BAAkC;AAAA,MAC/C,OAAO,EAAC,OAAO,4BAAkC;AAAA,MACjD,QAAQ,EAAC,OAAO,4BAAkC;AAAA,IAC1D,CAAC;AAAA,EAChB;AACA,SAAO,OAAO,GAAG;AACjB,MAAI;AAGH,WAAO,OAAO,GAAG,EAAE,QAAQ,WAAS,OAAO,OAAO,IAAI,CAAC;AACxD,SAAO;AACR;AAEA,SAAS,8BAA8B;AACtC,MAAI,CAAC;AACN;AAEO,SAAS,SAAS,KAAmB;AAC3C,SAAO,OAAO,SAAS,GAAG;AAC3B;;;ACjNA,IAAM,UAoBF,CAAC;AAIE,SAAS,UACf,WACiC;AACjC,QAAM,SAAS,QAAQ,SAAS;AAChC,MAAI,CAAC,QAAQ;AACZ,QAAI,GAAG,SAAS;AAAA,EACjB;AAEA,SAAO;AACR;AAEO,SAAS,WACf,WACA,gBACO;AACP,MAAI,CAAC,QAAQ,SAAS;AAAG,YAAQ,SAAS,IAAI;AAC/C;;;AC5BA,IAAI;AAEG,SAAS,kBAAkB;AACjC,SAAO;AACR;AAEA,SAAS,YACR,SACA,QACa;AACb,SAAO;AAAA,IACN,SAAS,CAAC;AAAA,IACV;AAAA,IACA;AAAA;AAAA;AAAA,IAGA,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,EACrB;AACD;AAEO,SAAS,kBACf,OACA,eACC;AACD,MAAI,eAAe;AAClB,cAAU,SAAS;AACnB,UAAM,WAAW,CAAC;AAClB,UAAM,kBAAkB,CAAC;AACzB,UAAM,iBAAiB;AAAA,EACxB;AACD;AAEO,SAAS,YAAY,OAAmB;AAC9C,aAAW,KAAK;AAChB,QAAM,QAAQ,QAAQ,WAAW;AAEjC,QAAM,UAAU;AACjB;AAEO,SAAS,WAAW,OAAmB;AAC7C,MAAI,UAAU,cAAc;AAC3B,mBAAe,MAAM;AAAA,EACtB;AACD;AAEO,SAAS,WAAWA,QAAc;AACxC,SAAQ,eAAe,YAAY,cAAcA,MAAK;AACvD;AAEA,SAAS,YAAY,OAAgB;AACpC,QAAM,QAAoB,MAAM,WAAW;AAC3C,MAAI,MAAM,4BAA6B,MAAM;AAC5C,UAAM,QAAQ;AAAA;AACV,UAAM,WAAW;AACvB;;;AC1DO,SAAS,cAAc,QAAa,OAAmB;AAC7D,QAAM,qBAAqB,MAAM,QAAQ;AACzC,QAAM,YAAY,MAAM,QAAS,CAAC;AAClC,QAAM,aAAa,WAAW,UAAa,WAAW;AACtD,MAAI,YAAY;AACf,QAAI,UAAU,WAAW,EAAE,WAAW;AACrC,kBAAY,KAAK;AACjB,UAAI,CAAC;AAAA,IACN;AACA,QAAI,YAAY,MAAM,GAAG;AAExB,eAAS,SAAS,OAAO,MAAM;AAC/B,UAAI,CAAC,MAAM;AAAS,oBAAY,OAAO,MAAM;AAAA,IAC9C;AACA,QAAI,MAAM,UAAU;AACnB,gBAAU,SAAS,EAAE;AAAA,QACpB,UAAU,WAAW,EAAE;AAAA,QACvB;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,IACD;AAAA,EACD,OAAO;AAEN,aAAS,SAAS,OAAO,WAAW,CAAC,CAAC;AAAA,EACvC;AACA,cAAY,KAAK;AACjB,MAAI,MAAM,UAAU;AACnB,UAAM,eAAgB,MAAM,UAAU,MAAM,eAAgB;AAAA,EAC7D;AACA,SAAO,WAAW,UAAU,SAAS;AACtC;AAEA,SAAS,SAAS,WAAuB,OAAY,MAAkB;AAEtE,MAAI,SAAS,KAAK;AAAG,WAAO;AAE5B,QAAM,QAAoB,MAAM,WAAW;AAE3C,MAAI,CAAC,OAAO;AACX;AAAA,MAAK;AAAA,MAAO,CAAC,KAAK,eACjB,iBAAiB,WAAW,OAAO,OAAO,KAAK,YAAY,IAAI;AAAA,IAChE;AACA,WAAO;AAAA,EACR;AAEA,MAAI,MAAM,WAAW;AAAW,WAAO;AAEvC,MAAI,CAAC,MAAM,WAAW;AACrB,gBAAY,WAAW,MAAM,OAAO,IAAI;AACxC,WAAO,MAAM;AAAA,EACd;AAEA,MAAI,CAAC,MAAM,YAAY;AACtB,UAAM,aAAa;AACnB,UAAM,OAAO;AACb,UAAM,SAAS,MAAM;AAKrB,QAAI,aAAa;AACjB,QAAIC,SAAQ;AACZ,QAAI,MAAM,uBAAwB;AACjC,mBAAa,IAAI,IAAI,MAAM;AAC3B,aAAO,MAAM;AACb,MAAAA,SAAQ;AAAA,IACT;AACA;AAAA,MAAK;AAAA,MAAY,CAAC,KAAK,eACtB,iBAAiB,WAAW,OAAO,QAAQ,KAAK,YAAY,MAAMA,MAAK;AAAA,IACxE;AAEA,gBAAY,WAAW,QAAQ,KAAK;AAEpC,QAAI,QAAQ,UAAU,UAAU;AAC/B,gBAAU,SAAS,EAAE;AAAA,QACpB;AAAA,QACA;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,MACX;AAAA,IACD;AAAA,EACD;AACA,SAAO,MAAM;AACd;AAEA,SAAS,iBACR,WACA,aACA,cACA,MACA,YACA,UACA,aACC;AACD,MAAI,QAAQ,IAAI,aAAa,gBAAgB,eAAe;AAC3D,QAAI,CAAC;AACN,MAAI,QAAQ,UAAU,GAAG;AACxB,UAAM,OACL,YACA,eACA,YAAa;AAAA,IACb,CAAC,IAAK,YAA8C,WAAY,IAAI,IACjE,SAAU,OAAO,IAAI,IACrB;AAEJ,UAAM,MAAM,SAAS,WAAW,YAAY,IAAI;AAChD,QAAI,cAAc,MAAM,GAAG;AAG3B,QAAI,QAAQ,GAAG,GAAG;AACjB,gBAAU,iBAAiB;AAAA,IAC5B;AAAO;AAAA,EACR,WAAW,aAAa;AACvB,iBAAa,IAAI,UAAU;AAAA,EAC5B;AAEA,MAAI,YAAY,UAAU,KAAK,CAAC,SAAS,UAAU,GAAG;AACrD,QAAI,CAAC,UAAU,OAAO,eAAe,UAAU,qBAAqB,GAAG;AAMtE;AAAA,IACD;AACA,aAAS,WAAW,UAAU;AAI9B,SACE,CAAC,eAAe,CAAC,YAAY,OAAO,YACrC,OAAO,SAAS,aACf,MAAM,YAAY,IAChB,aAAa,IAAI,IAAI,IACrB,OAAO,UAAU,qBAAqB,KAAK,cAAc,IAAI;AAEhE,kBAAY,WAAW,UAAU;AAAA,EACnC;AACD;AAEA,SAAS,YAAY,OAAmB,OAAY,OAAO,OAAO;AAEjE,MAAI,CAAC,MAAM,WAAW,MAAM,OAAO,eAAe,MAAM,gBAAgB;AACvE,WAAO,OAAO,IAAI;AAAA,EACnB;AACD;;;ACpHO,SAAS,iBACf,MACA,QACyB;AACzB,QAAM,UAAU,MAAM,QAAQ,IAAI;AAClC,QAAM,QAAoB;AAAA,IACzB,OAAO;AAAA;AAAA,IAEP,QAAQ,SAAS,OAAO,SAAS,gBAAgB;AAAA;AAAA,IAEjD,WAAW;AAAA;AAAA,IAEX,YAAY;AAAA;AAAA,IAEZ,WAAW,CAAC;AAAA;AAAA,IAEZ,SAAS;AAAA;AAAA,IAET,OAAO;AAAA;AAAA,IAEP,QAAQ;AAAA;AAAA;AAAA,IAER,OAAO;AAAA;AAAA,IAEP,SAAS;AAAA,IACT,WAAW;AAAA,EACZ;AAQA,MAAI,SAAY;AAChB,MAAI,QAA2C;AAC/C,MAAI,SAAS;AACZ,aAAS,CAAC,KAAK;AACf,YAAQ;AAAA,EACT;AAEA,QAAM,EAAC,QAAQ,MAAK,IAAI,MAAM,UAAU,QAAQ,KAAK;AACrD,QAAM,SAAS;AACf,QAAM,UAAU;AAChB,SAAO;AACR;AAKO,IAAM,cAAwC;AAAA,EACpD,IAAI,OAAO,MAAM;AAChB,QAAI,SAAS;AAAa,aAAO;AAEjC,UAAM,SAAS,OAAO,KAAK;AAC3B,QAAI,CAAC,IAAI,QAAQ,IAAI,GAAG;AAEvB,aAAO,kBAAkB,OAAO,QAAQ,IAAI;AAAA,IAC7C;AACA,UAAM,QAAQ,OAAO,IAAI;AACzB,QAAI,MAAM,cAAc,CAAC,YAAY,KAAK,GAAG;AAC5C,aAAO;AAAA,IACR;AAGA,QAAI,UAAU,KAAK,MAAM,OAAO,IAAI,GAAG;AACtC,kBAAY,KAAK;AACjB,aAAQ,MAAM,MAAO,IAAW,IAAI,YAAY,OAAO,KAAK;AAAA,IAC7D;AACA,WAAO;AAAA,EACR;AAAA,EACA,IAAI,OAAO,MAAM;AAChB,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC5B;AAAA,EACA,QAAQ,OAAO;AACd,WAAO,QAAQ,QAAQ,OAAO,KAAK,CAAC;AAAA,EACrC;AAAA,EACA,IACC,OACA,MACA,OACC;AACD,UAAM,OAAO,uBAAuB,OAAO,KAAK,GAAG,IAAI;AACvD,QAAI,6BAAM,KAAK;AAGd,WAAK,IAAI,KAAK,MAAM,QAAQ,KAAK;AACjC,aAAO;AAAA,IACR;AACA,QAAI,CAAC,MAAM,WAAW;AAGrB,YAAMC,WAAU,KAAK,OAAO,KAAK,GAAG,IAAI;AAExC,YAAM,eAAiCA,YAAA,gBAAAA,SAAU;AACjD,UAAI,gBAAgB,aAAa,UAAU,OAAO;AACjD,cAAM,MAAO,IAAI,IAAI;AACrB,cAAM,UAAU,IAAI,IAAI;AACxB,eAAO;AAAA,MACR;AACA,UAAI,GAAG,OAAOA,QAAO,MAAM,UAAU,UAAa,IAAI,MAAM,OAAO,IAAI;AACtE,eAAO;AACR,kBAAY,KAAK;AACjB,kBAAY,KAAK;AAAA,IAClB;AAEA,QACE,MAAM,MAAO,IAAI,MAAM;AAAA,KAEtB,UAAU,UAAa,QAAQ,MAAM;AAAA,IAEtC,OAAO,MAAM,KAAK,KAAK,OAAO,MAAM,MAAM,MAAO,IAAI,CAAC;AAEvD,aAAO;AAGR,UAAM,MAAO,IAAI,IAAI;AACrB,UAAM,UAAU,IAAI,IAAI;AACxB,WAAO;AAAA,EACR;AAAA,EACA,eAAe,OAAO,MAAc;AAEnC,QAAI,KAAK,MAAM,OAAO,IAAI,MAAM,UAAa,QAAQ,MAAM,OAAO;AACjE,YAAM,UAAU,IAAI,IAAI;AACxB,kBAAY,KAAK;AACjB,kBAAY,KAAK;AAAA,IAClB,OAAO;AAEN,aAAO,MAAM,UAAU,IAAI;AAAA,IAC5B;AACA,QAAI,MAAM,OAAO;AAChB,aAAO,MAAM,MAAM,IAAI;AAAA,IACxB;AACA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA,EAGA,yBAAyB,OAAO,MAAM;AACrC,UAAM,QAAQ,OAAO,KAAK;AAC1B,UAAM,OAAO,QAAQ,yBAAyB,OAAO,IAAI;AACzD,QAAI,CAAC;AAAM,aAAO;AAClB,WAAO;AAAA,MACN,UAAU;AAAA,MACV,cAAc,MAAM,2BAA4B,SAAS;AAAA,MACzD,YAAY,KAAK;AAAA,MACjB,OAAO,MAAM,IAAI;AAAA,IAClB;AAAA,EACD;AAAA,EACA,iBAAiB;AAChB,QAAI,EAAE;AAAA,EACP;AAAA,EACA,eAAe,OAAO;AACrB,WAAO,eAAe,MAAM,KAAK;AAAA,EAClC;AAAA,EACA,iBAAiB;AAChB,QAAI,EAAE;AAAA,EACP;AACD;AAMA,IAAM,aAA8C,CAAC;AACrD,KAAK,aAAa,CAAC,KAAK,OAAO;AAE9B,aAAW,GAAG,IAAI,WAAW;AAC5B,cAAU,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC;AAC7B,WAAO,GAAG,MAAM,MAAM,SAAS;AAAA,EAChC;AACD,CAAC;AACD,WAAW,iBAAiB,SAAS,OAAO,MAAM;AACjD,MAAI,QAAQ,IAAI,aAAa,gBAAgB,MAAM,SAAS,IAAW,CAAC;AACvE,QAAI,EAAE;AAEP,SAAO,WAAW,IAAK,KAAK,MAAM,OAAO,MAAM,MAAS;AACzD;AACA,WAAW,MAAM,SAAS,OAAO,MAAM,OAAO;AAC7C,MACC,QAAQ,IAAI,aAAa,gBACzB,SAAS,YACT,MAAM,SAAS,IAAW,CAAC;AAE3B,QAAI,EAAE;AACP,SAAO,YAAY,IAAK,KAAK,MAAM,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM,CAAC,CAAC;AACnE;AAGA,SAAS,KAAK,OAAgB,MAAmB;AAChD,QAAM,QAAQ,MAAM,WAAW;AAC/B,QAAM,SAAS,QAAQ,OAAO,KAAK,IAAI;AACvC,SAAO,OAAO,IAAI;AACnB;AAEA,SAAS,kBAAkB,OAAmB,QAAa,MAAmB;AArP9E;AAsPC,QAAM,OAAO,uBAAuB,QAAQ,IAAI;AAChD,SAAO,OACJ,WAAW,OACV,KAAK;AAAA;AAAA;AAAA,KAGL,UAAK,QAAL,mBAAU,KAAK,MAAM;AAAA,MACtB;AACJ;AAEA,SAAS,uBACR,QACA,MACiC;AAEjC,MAAI,EAAE,QAAQ;AAAS,WAAO;AAC9B,MAAI,QAAQ,eAAe,MAAM;AACjC,SAAO,OAAO;AACb,UAAM,OAAO,OAAO,yBAAyB,OAAO,IAAI;AACxD,QAAI;AAAM,aAAO;AACjB,YAAQ,eAAe,KAAK;AAAA,EAC7B;AACA,SAAO;AACR;AAEO,SAAS,YAAY,OAAmB;AAC9C,MAAI,CAAC,MAAM,WAAW;AACrB,UAAM,YAAY;AAClB,QAAI,MAAM,SAAS;AAClB,kBAAY,MAAM,OAAO;AAAA,IAC1B;AAAA,EACD;AACD;AAEO,SAAS,YAAY,OAIzB;AACF,MAAI,CAAC,MAAM,OAAO;AACjB,UAAM,QAAQ;AAAA,MACb,MAAM;AAAA,MACN,MAAM,OAAO,OAAO;AAAA,IACrB;AAAA,EACD;AACD;;;AChQO,IAAMC,SAAN,MAAoC;AAAA,EAI1C,YAAY,QAGT;AANH,uBAAuB;AACvB,iCAAoC;AA+BpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAAoB,CAAC,MAAW,QAAc,kBAAwB;AAErE,UAAI,OAAO,SAAS,cAAc,OAAO,WAAW,YAAY;AAC/D,cAAM,cAAc;AACpB,iBAAS;AAET,cAAM,OAAO;AACb,eAAO,SAAS,eAEfC,QAAO,gBACJ,MACF;AACD,iBAAO,KAAK,QAAQA,OAAM,CAAC,UAAmB,OAAO,KAAK,MAAM,OAAO,GAAG,IAAI,CAAC;AAAA,QAChF;AAAA,MACD;AAEA,UAAI,OAAO,WAAW;AAAY,YAAI,CAAC;AACvC,UAAI,kBAAkB,UAAa,OAAO,kBAAkB;AAC3D,YAAI,CAAC;AAEN,UAAI;AAGJ,UAAI,YAAY,IAAI,GAAG;AACtB,cAAM,QAAQ,WAAW,IAAI;AAC7B,cAAM,QAAQ,YAAY,MAAM,MAAS;AACzC,YAAI,WAAW;AACf,YAAI;AACH,mBAAS,OAAO,KAAK;AACrB,qBAAW;AAAA,QACZ,UAAE;AAED,cAAI;AAAU,wBAAY,KAAK;AAAA;AAC1B,uBAAW,KAAK;AAAA,QACtB;AACA,0BAAkB,OAAO,aAAa;AACtC,eAAO,cAAc,QAAQ,KAAK;AAAA,MACnC,WAAW,CAAC,QAAQ,OAAO,SAAS,UAAU;AAC7C,iBAAS,OAAO,IAAI;AACpB,YAAI,WAAW;AAAW,mBAAS;AACnC,YAAI,WAAW;AAAS,mBAAS;AACjC,YAAI,KAAK;AAAa,iBAAO,QAAQ,IAAI;AACzC,YAAI,eAAe;AAClB,gBAAM,IAAa,CAAC;AACpB,gBAAM,KAAc,CAAC;AACrB,oBAAU,SAAS,EAAE,4BAA4B,MAAM,QAAQ,GAAG,EAAE;AACpE,wBAAc,GAAG,EAAE;AAAA,QACpB;AACA,eAAO;AAAA,MACR;AAAO,YAAI,GAAG,IAAI;AAAA,IACnB;AAEA,8BAA0C,CAAC,MAAW,WAAsB;AAE3E,UAAI,OAAO,SAAS,YAAY;AAC/B,eAAO,CAAC,UAAe,SACtB,KAAK,mBAAmB,OAAO,CAAC,UAAe,KAAK,OAAO,GAAG,IAAI,CAAC;AAAA,MACrE;AAEA,UAAI,SAAkB;AACtB,YAAM,SAAS,KAAK,QAAQ,MAAM,QAAQ,CAAC,GAAY,OAAgB;AACtE,kBAAU;AACV,yBAAiB;AAAA,MAClB,CAAC;AACD,aAAO,CAAC,QAAQ,SAAU,cAAe;AAAA,IAC1C;AA1FC,QAAI,QAAO,iCAAQ,gBAAe;AACjC,WAAK,cAAc,OAAQ,UAAU;AACtC,QAAI,QAAO,iCAAQ,0BAAyB;AAC3C,WAAK,wBAAwB,OAAQ,oBAAoB;AAAA,EAC3D;AAAA,EAwFA,YAAiC,MAAmB;AACnD,QAAI,CAAC,YAAY,IAAI;AAAG,UAAI,CAAC;AAC7B,QAAI,QAAQ,IAAI;AAAG,aAAO,QAAQ,IAAI;AACtC,UAAM,QAAQ,WAAW,IAAI;AAC7B,UAAM,QAAQ,YAAY,MAAM,MAAS;AACzC,UAAM,WAAW,EAAE,YAAY;AAC/B,eAAW,KAAK;AAChB,WAAO;AAAA,EACR;AAAA,EAEA,YACC,OACA,eACuC;AACvC,UAAM,QAAoB,SAAU,MAAc,WAAW;AAC7D,QAAI,CAAC,SAAS,CAAC,MAAM;AAAW,UAAI,CAAC;AACrC,UAAM,EAAC,QAAQ,MAAK,IAAI;AACxB,sBAAkB,OAAO,aAAa;AACtC,WAAO,cAAc,QAAW,KAAK;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,cAAc,OAAgB;AAC7B,SAAK,cAAc;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,wBAAwB,OAAmB;AAC1C,SAAK,wBAAwB;AAAA,EAC9B;AAAA,EAEA,aAAkC,MAAS,SAA8B;AAGxE,QAAI;AACJ,SAAK,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,YAAM,QAAQ,QAAQ,CAAC;AACvB,UAAI,MAAM,KAAK,WAAW,KAAK,MAAM,OAAO,WAAW;AACtD,eAAO,MAAM;AACb;AAAA,MACD;AAAA,IACD;AAGA,QAAI,IAAI,IAAI;AACX,gBAAU,QAAQ,MAAM,IAAI,CAAC;AAAA,IAC9B;AAEA,UAAM,mBAAmB,UAAU,SAAS,EAAE;AAC9C,QAAI,QAAQ,IAAI,GAAG;AAElB,aAAO,iBAAiB,MAAM,OAAO;AAAA,IACtC;AAEA,WAAO,KAAK;AAAA,MAAQ;AAAA,MAAM,CAAC,UAC1B,iBAAiB,OAAO,OAAO;AAAA,IAChC;AAAA,EACD;AACD;AAEO,SAAS,YACf,OACA,QACyB;AAEzB,QAAM,QAAiB,MAAM,KAAK,IAC/B,UAAU,QAAQ,EAAE,UAAU,OAAO,MAAM,IAC3C,MAAM,KAAK,IACX,UAAU,QAAQ,EAAE,UAAU,OAAO,MAAM,IAC3C,iBAAiB,OAAO,MAAM;AAEjC,QAAM,QAAQ,SAAS,OAAO,SAAS,gBAAgB;AACvD,QAAM,QAAQ,KAAK,KAAK;AACxB,SAAO;AACR;;;AC3MO,SAAS,QAAQ,OAAiB;AACxC,MAAI,CAAC,QAAQ,KAAK;AAAG,QAAI,IAAI,KAAK;AAClC,SAAO,YAAY,KAAK;AACzB;AAEA,SAAS,YAAY,OAAiB;AACrC,MAAI,CAAC,YAAY,KAAK,KAAK,SAAS,KAAK;AAAG,WAAO;AACnD,QAAM,QAAgC,MAAM,WAAW;AACvD,MAAI;AACJ,MAAI,OAAO;AACV,QAAI,CAAC,MAAM;AAAW,aAAO,MAAM;AAEnC,UAAM,aAAa;AACnB,WAAO,YAAY,OAAO,MAAM,OAAO,OAAO,qBAAqB;AAAA,EACpE,OAAO;AACN,WAAO,YAAY,OAAO,IAAI;AAAA,EAC/B;AAEA,OAAK,MAAM,CAAC,KAAK,eAAe;AAC/B,QAAI,MAAM,KAAK,YAAY,UAAU,CAAC;AAAA,EACvC,CAAC;AACD,MAAI,OAAO;AACV,UAAM,aAAa;AAAA,EACpB;AACA,SAAO;AACR;;;ACdO,SAAS,gBAAgB;AAC/B,QAAM,cAAc;AACpB,MAAI,QAAQ,IAAI,aAAa,cAAc;AAC1C,WAAO;AAAA,MACN;AAAA,MACA,SAAS,IAAY;AACpB,eAAO,kCAAkC;AAAA,MAC1C;AAAA,MACA,SAAS,MAAc;AACtB,eAAO,+CAA+C;AAAA,MACvD;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAEA,QAAM,UAAU;AAChB,QAAM,MAAM;AACZ,QAAM,SAAS;AAEf,WAAS,iBACR,OACA,UACA,SACA,gBACO;AACP,YAAQ,MAAM,OAAO;AAAA,MACpB;AAAA,MACA;AACC,eAAO;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,MACD;AACC,eAAO,qBAAqB,OAAO,UAAU,SAAS,cAAc;AAAA,MACrE;AACC,eAAO;AAAA,UACL;AAAA,UACD;AAAA,UACA;AAAA,UACA;AAAA,QACD;AAAA,IACF;AAAA,EACD;AAEA,WAAS,qBACR,OACA,UACA,SACA,gBACC;AACD,QAAI,EAAC,OAAO,UAAS,IAAI;AACzB,QAAI,QAAQ,MAAM;AAGlB,QAAI,MAAM,SAAS,MAAM,QAAQ;AAEhC;AAAC,OAAC,OAAO,KAAK,IAAI,CAAC,OAAO,KAAK;AAC9B,OAAC,SAAS,cAAc,IAAI,CAAC,gBAAgB,OAAO;AAAA,IACtD;AAGA,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI,UAAU,CAAC,KAAK,MAAM,CAAC,MAAM,MAAM,CAAC,GAAG;AAC1C,cAAM,OAAO,SAAS,OAAO,CAAC,CAAC,CAAC;AAChC,gBAAQ,KAAK;AAAA,UACZ,IAAI;AAAA,UACJ;AAAA;AAAA;AAAA,UAGA,OAAO,wBAAwB,MAAM,CAAC,CAAC;AAAA,QACxC,CAAC;AACD,uBAAe,KAAK;AAAA,UACnB,IAAI;AAAA,UACJ;AAAA,UACA,OAAO,wBAAwB,MAAM,CAAC,CAAC;AAAA,QACxC,CAAC;AAAA,MACF;AAAA,IACD;AAGA,aAAS,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,KAAK;AACjD,YAAM,OAAO,SAAS,OAAO,CAAC,CAAC,CAAC;AAChC,cAAQ,KAAK;AAAA,QACZ,IAAI;AAAA,QACJ;AAAA;AAAA;AAAA,QAGA,OAAO,wBAAwB,MAAM,CAAC,CAAC;AAAA,MACxC,CAAC;AAAA,IACF;AACA,aAAS,IAAI,MAAM,SAAS,GAAG,MAAM,UAAU,GAAG,EAAE,GAAG;AACtD,YAAM,OAAO,SAAS,OAAO,CAAC,CAAC,CAAC;AAChC,qBAAe,KAAK;AAAA,QACnB,IAAI;AAAA,QACJ;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AAGA,WAAS,4BACR,OACA,UACA,SACA,gBACC;AACD,UAAM,EAAC,OAAO,MAAK,IAAI;AACvB,SAAK,MAAM,WAAY,CAAC,KAAK,kBAAkB;AAC9C,YAAM,YAAY,IAAI,OAAO,GAAG;AAChC,YAAM,QAAQ,IAAI,OAAQ,GAAG;AAC7B,YAAM,KAAK,CAAC,gBAAgB,SAAS,IAAI,OAAO,GAAG,IAAI,UAAU;AACjE,UAAI,cAAc,SAAS,OAAO;AAAS;AAC3C,YAAM,OAAO,SAAS,OAAO,GAAU;AACvC,cAAQ,KAAK,OAAO,SAAS,EAAC,IAAI,KAAI,IAAI,EAAC,IAAI,MAAM,MAAK,CAAC;AAC3D,qBAAe;AAAA,QACd,OAAO,MACJ,EAAC,IAAI,QAAQ,KAAI,IACjB,OAAO,SACP,EAAC,IAAI,KAAK,MAAM,OAAO,wBAAwB,SAAS,EAAC,IACzD,EAAC,IAAI,SAAS,MAAM,OAAO,wBAAwB,SAAS,EAAC;AAAA,MACjE;AAAA,IACD,CAAC;AAAA,EACF;AAEA,WAAS,mBACR,OACA,UACA,SACA,gBACC;AACD,QAAI,EAAC,OAAO,MAAK,IAAI;AAErB,QAAI,IAAI;AACR,UAAM,QAAQ,CAAC,UAAe;AAC7B,UAAI,CAAC,MAAO,IAAI,KAAK,GAAG;AACvB,cAAM,OAAO,SAAS,OAAO,CAAC,CAAC,CAAC;AAChC,gBAAQ,KAAK;AAAA,UACZ,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,QACD,CAAC;AACD,uBAAe,QAAQ;AAAA,UACtB,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,QACD,CAAC;AAAA,MACF;AACA;AAAA,IACD,CAAC;AACD,QAAI;AACJ,UAAO,QAAQ,CAAC,UAAe;AAC9B,UAAI,CAAC,MAAM,IAAI,KAAK,GAAG;AACtB,cAAM,OAAO,SAAS,OAAO,CAAC,CAAC,CAAC;AAChC,gBAAQ,KAAK;AAAA,UACZ,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,QACD,CAAC;AACD,uBAAe,QAAQ;AAAA,UACtB,IAAI;AAAA,UACJ;AAAA,UACA;AAAA,QACD,CAAC;AAAA,MACF;AACA;AAAA,IACD,CAAC;AAAA,EACF;AAEA,WAAS,4BACR,WACA,aACA,SACA,gBACO;AACP,YAAQ,KAAK;AAAA,MACZ,IAAI;AAAA,MACJ,MAAM,CAAC;AAAA,MACP,OAAO,gBAAgB,UAAU,SAAY;AAAA,IAC9C,CAAC;AACD,mBAAe,KAAK;AAAA,MACnB,IAAI;AAAA,MACJ,MAAM,CAAC;AAAA,MACP,OAAO;AAAA,IACR,CAAC;AAAA,EACF;AAEA,WAAS,cAAiB,OAAU,SAA8B;AACjE,YAAQ,QAAQ,WAAS;AACxB,YAAM,EAAC,MAAM,GAAE,IAAI;AAEnB,UAAI,OAAY;AAChB,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,GAAG,KAAK;AACzC,cAAM,aAAa,YAAY,IAAI;AACnC,YAAI,IAAI,KAAK,CAAC;AACd,YAAI,OAAO,MAAM,YAAY,OAAO,MAAM,UAAU;AACnD,cAAI,KAAK;AAAA,QACV;AAGA,aACE,iCAAkC,kCAClC,MAAM,eAAe,MAAM;AAE5B,cAAI,cAAc,CAAC;AACpB,YAAI,OAAO,SAAS,cAAc,MAAM;AACvC,cAAI,cAAc,CAAC;AACpB,eAAO,IAAI,MAAM,CAAC;AAClB,YAAI,OAAO,SAAS;AAAU,cAAI,cAAc,GAAG,KAAK,KAAK,GAAG,CAAC;AAAA,MAClE;AAEA,YAAM,OAAO,YAAY,IAAI;AAC7B,YAAM,QAAQ,oBAAoB,MAAM,KAAK;AAC7C,YAAM,MAAM,KAAK,KAAK,SAAS,CAAC;AAChC,cAAQ,IAAI;AAAA,QACX,KAAK;AACJ,kBAAQ,MAAM;AAAA,YACb;AACC,qBAAO,KAAK,IAAI,KAAK,KAAK;AAAA,YAE3B;AACC,kBAAI,WAAW;AAAA,YAChB;AAKC,qBAAQ,KAAK,GAAG,IAAI;AAAA,UACtB;AAAA,QACD,KAAK;AACJ,kBAAQ,MAAM;AAAA,YACb;AACC,qBAAO,QAAQ,MACZ,KAAK,KAAK,KAAK,IACf,KAAK,OAAO,KAAY,GAAG,KAAK;AAAA,YACpC;AACC,qBAAO,KAAK,IAAI,KAAK,KAAK;AAAA,YAC3B;AACC,qBAAO,KAAK,IAAI,KAAK;AAAA,YACtB;AACC,qBAAQ,KAAK,GAAG,IAAI;AAAA,UACtB;AAAA,QACD,KAAK;AACJ,kBAAQ,MAAM;AAAA,YACb;AACC,qBAAO,KAAK,OAAO,KAAY,CAAC;AAAA,YACjC;AACC,qBAAO,KAAK,OAAO,GAAG;AAAA,YACvB;AACC,qBAAO,KAAK,OAAO,MAAM,KAAK;AAAA,YAC/B;AACC,qBAAO,OAAO,KAAK,GAAG;AAAA,UACxB;AAAA,QACD;AACC,cAAI,cAAc,GAAG,EAAE;AAAA,MACzB;AAAA,IACD,CAAC;AAED,WAAO;AAAA,EACR;AAMA,WAAS,oBAAoB,KAAU;AACtC,QAAI,CAAC,YAAY,GAAG;AAAG,aAAO;AAC9B,QAAI,MAAM,QAAQ,GAAG;AAAG,aAAO,IAAI,IAAI,mBAAmB;AAC1D,QAAI,MAAM,GAAG;AACZ,aAAO,IAAI;AAAA,QACV,MAAM,KAAK,IAAI,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,oBAAoB,CAAC,CAAC,CAAC;AAAA,MACtE;AACD,QAAI,MAAM,GAAG;AAAG,aAAO,IAAI,IAAI,MAAM,KAAK,GAAG,EAAE,IAAI,mBAAmB,CAAC;AACvE,UAAM,SAAS,OAAO,OAAO,eAAe,GAAG,CAAC;AAChD,eAAW,OAAO;AAAK,aAAO,GAAG,IAAI,oBAAoB,IAAI,GAAG,CAAC;AACjE,QAAI,IAAI,KAAK,SAAS;AAAG,aAAO,SAAS,IAAI,IAAI,SAAS;AAC1D,WAAO;AAAA,EACR;AAEA,WAAS,wBAA2B,KAAW;AAC9C,QAAI,QAAQ,GAAG,GAAG;AACjB,aAAO,oBAAoB,GAAG;AAAA,IAC/B;AAAO,aAAO;AAAA,EACf;AAEA,aAAW,WAAW;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,EACD,CAAC;AACF;;;ACzSO,SAAS,eAAe;AAC9B,QAAM,iBAAiB,IAAI;AAAA,IAG1B,YAAY,QAAgB,QAAqB;AAChD,YAAM;AACN,WAAK,WAAW,IAAI;AAAA,QACnB;AAAA,QACA,SAAS;AAAA,QACT,QAAQ,SAAS,OAAO,SAAS,gBAAgB;AAAA,QACjD,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,UAAU;AAAA,MACX;AAAA,IACD;AAAA,IAEA,IAAI,OAAe;AAClB,aAAO,OAAO,KAAK,WAAW,CAAC,EAAE;AAAA,IAClC;AAAA,IAEA,IAAI,KAAmB;AACtB,aAAO,OAAO,KAAK,WAAW,CAAC,EAAE,IAAI,GAAG;AAAA,IACzC;AAAA,IAEA,IAAI,KAAU,OAAY;AACzB,YAAM,QAAkB,KAAK,WAAW;AACxC,sBAAgB,KAAK;AACrB,UAAI,CAAC,OAAO,KAAK,EAAE,IAAI,GAAG,KAAK,OAAO,KAAK,EAAE,IAAI,GAAG,MAAM,OAAO;AAChE,uBAAe,KAAK;AACpB,oBAAY,KAAK;AACjB,cAAM,UAAW,IAAI,KAAK,IAAI;AAC9B,cAAM,MAAO,IAAI,KAAK,KAAK;AAC3B,cAAM,UAAW,IAAI,KAAK,IAAI;AAAA,MAC/B;AACA,aAAO;AAAA,IACR;AAAA,IAEA,OAAO,KAAmB;AACzB,UAAI,CAAC,KAAK,IAAI,GAAG,GAAG;AACnB,eAAO;AAAA,MACR;AAEA,YAAM,QAAkB,KAAK,WAAW;AACxC,sBAAgB,KAAK;AACrB,qBAAe,KAAK;AACpB,kBAAY,KAAK;AACjB,UAAI,MAAM,MAAM,IAAI,GAAG,GAAG;AACzB,cAAM,UAAW,IAAI,KAAK,KAAK;AAAA,MAChC,OAAO;AACN,cAAM,UAAW,OAAO,GAAG;AAAA,MAC5B;AACA,YAAM,MAAO,OAAO,GAAG;AACvB,aAAO;AAAA,IACR;AAAA,IAEA,QAAQ;AACP,YAAM,QAAkB,KAAK,WAAW;AACxC,sBAAgB,KAAK;AACrB,UAAI,OAAO,KAAK,EAAE,MAAM;AACvB,uBAAe,KAAK;AACpB,oBAAY,KAAK;AACjB,cAAM,YAAY,oBAAI,IAAI;AAC1B,aAAK,MAAM,OAAO,SAAO;AACxB,gBAAM,UAAW,IAAI,KAAK,KAAK;AAAA,QAChC,CAAC;AACD,cAAM,MAAO,MAAM;AAAA,MACpB;AAAA,IACD;AAAA,IAEA,QAAQ,IAA+C,SAAe;AACrE,YAAM,QAAkB,KAAK,WAAW;AACxC,aAAO,KAAK,EAAE,QAAQ,CAAC,QAAa,KAAU,SAAc;AAC3D,WAAG,KAAK,SAAS,KAAK,IAAI,GAAG,GAAG,KAAK,IAAI;AAAA,MAC1C,CAAC;AAAA,IACF;AAAA,IAEA,IAAI,KAAe;AAClB,YAAM,QAAkB,KAAK,WAAW;AACxC,sBAAgB,KAAK;AACrB,YAAM,QAAQ,OAAO,KAAK,EAAE,IAAI,GAAG;AACnC,UAAI,MAAM,cAAc,CAAC,YAAY,KAAK,GAAG;AAC5C,eAAO;AAAA,MACR;AACA,UAAI,UAAU,MAAM,MAAM,IAAI,GAAG,GAAG;AACnC,eAAO;AAAA,MACR;AAEA,YAAM,QAAQ,YAAY,OAAO,KAAK;AACtC,qBAAe,KAAK;AACpB,YAAM,MAAO,IAAI,KAAK,KAAK;AAC3B,aAAO;AAAA,IACR;AAAA,IAEA,OAA8B;AAC7B,aAAO,OAAO,KAAK,WAAW,CAAC,EAAE,KAAK;AAAA,IACvC;AAAA,IAEA,SAAgC;AAC/B,YAAM,WAAW,KAAK,KAAK;AAC3B,aAAO;AAAA,QACN,CAAC,OAAO,QAAQ,GAAG,MAAM,KAAK,OAAO;AAAA,QACrC,MAAM,MAAM;AACX,gBAAM,IAAI,SAAS,KAAK;AAExB,cAAI,EAAE;AAAM,mBAAO;AACnB,gBAAM,QAAQ,KAAK,IAAI,EAAE,KAAK;AAC9B,iBAAO;AAAA,YACN,MAAM;AAAA,YACN;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IAEA,UAAwC;AACvC,YAAM,WAAW,KAAK,KAAK;AAC3B,aAAO;AAAA,QACN,CAAC,OAAO,QAAQ,GAAG,MAAM,KAAK,QAAQ;AAAA,QACtC,MAAM,MAAM;AACX,gBAAM,IAAI,SAAS,KAAK;AAExB,cAAI,EAAE;AAAM,mBAAO;AACnB,gBAAM,QAAQ,KAAK,IAAI,EAAE,KAAK;AAC9B,iBAAO;AAAA,YACN,MAAM;AAAA,YACN,OAAO,CAAC,EAAE,OAAO,KAAK;AAAA,UACvB;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,IAEA,EAtIC,aAsIA,OAAO,SAAQ,IAAI;AACnB,aAAO,KAAK,QAAQ;AAAA,IACrB;AAAA,EACD;AAEA,WAAS,UAA4B,QAAW,QAAwB;AAEvE,WAAO,IAAI,SAAS,QAAQ,MAAM;AAAA,EACnC;AAEA,WAAS,eAAe,OAAiB;AACxC,QAAI,CAAC,MAAM,OAAO;AACjB,YAAM,YAAY,oBAAI,IAAI;AAC1B,YAAM,QAAQ,IAAI,IAAI,MAAM,KAAK;AAAA,IAClC;AAAA,EACD;AAEA,QAAM,iBAAiB,IAAI;AAAA,IAE1B,YAAY,QAAgB,QAAqB;AAChD,YAAM;AACN,WAAK,WAAW,IAAI;AAAA,QACnB;AAAA,QACA,SAAS;AAAA,QACT,QAAQ,SAAS,OAAO,SAAS,gBAAgB;AAAA,QACjD,WAAW;AAAA,QACX,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS,oBAAI,IAAI;AAAA,QACjB,UAAU;AAAA,QACV,WAAW;AAAA,MACZ;AAAA,IACD;AAAA,IAEA,IAAI,OAAe;AAClB,aAAO,OAAO,KAAK,WAAW,CAAC,EAAE;AAAA,IAClC;AAAA,IAEA,IAAI,OAAqB;AACxB,YAAM,QAAkB,KAAK,WAAW;AACxC,sBAAgB,KAAK;AAErB,UAAI,CAAC,MAAM,OAAO;AACjB,eAAO,MAAM,MAAM,IAAI,KAAK;AAAA,MAC7B;AACA,UAAI,MAAM,MAAM,IAAI,KAAK;AAAG,eAAO;AACnC,UAAI,MAAM,QAAQ,IAAI,KAAK,KAAK,MAAM,MAAM,IAAI,MAAM,QAAQ,IAAI,KAAK,CAAC;AACvE,eAAO;AACR,aAAO;AAAA,IACR;AAAA,IAEA,IAAI,OAAiB;AACpB,YAAM,QAAkB,KAAK,WAAW;AACxC,sBAAgB,KAAK;AACrB,UAAI,CAAC,KAAK,IAAI,KAAK,GAAG;AACrB,uBAAe,KAAK;AACpB,oBAAY,KAAK;AACjB,cAAM,MAAO,IAAI,KAAK;AAAA,MACvB;AACA,aAAO;AAAA,IACR;AAAA,IAEA,OAAO,OAAiB;AACvB,UAAI,CAAC,KAAK,IAAI,KAAK,GAAG;AACrB,eAAO;AAAA,MACR;AAEA,YAAM,QAAkB,KAAK,WAAW;AACxC,sBAAgB,KAAK;AACrB,qBAAe,KAAK;AACpB,kBAAY,KAAK;AACjB,aACC,MAAM,MAAO,OAAO,KAAK,MACxB,MAAM,QAAQ,IAAI,KAAK,IACrB,MAAM,MAAO,OAAO,MAAM,QAAQ,IAAI,KAAK,CAAC;AAAA;AAAA,QACjB;AAAA;AAAA,IAEhC;AAAA,IAEA,QAAQ;AACP,YAAM,QAAkB,KAAK,WAAW;AACxC,sBAAgB,KAAK;AACrB,UAAI,OAAO,KAAK,EAAE,MAAM;AACvB,uBAAe,KAAK;AACpB,oBAAY,KAAK;AACjB,cAAM,MAAO,MAAM;AAAA,MACpB;AAAA,IACD;AAAA,IAEA,SAAgC;AAC/B,YAAM,QAAkB,KAAK,WAAW;AACxC,sBAAgB,KAAK;AACrB,qBAAe,KAAK;AACpB,aAAO,MAAM,MAAO,OAAO;AAAA,IAC5B;AAAA,IAEA,UAAwC;AACvC,YAAM,QAAkB,KAAK,WAAW;AACxC,sBAAgB,KAAK;AACrB,qBAAe,KAAK;AACpB,aAAO,MAAM,MAAO,QAAQ;AAAA,IAC7B;AAAA,IAEA,OAA8B;AAC7B,aAAO,KAAK,OAAO;AAAA,IACpB;AAAA,IAEA,EA3FC,aA2FA,OAAO,SAAQ,IAAI;AACnB,aAAO,KAAK,OAAO;AAAA,IACpB;AAAA,IAEA,QAAQ,IAAS,SAAe;AAC/B,YAAM,WAAW,KAAK,OAAO;AAC7B,UAAI,SAAS,SAAS,KAAK;AAC3B,aAAO,CAAC,OAAO,MAAM;AACpB,WAAG,KAAK,SAAS,OAAO,OAAO,OAAO,OAAO,IAAI;AACjD,iBAAS,SAAS,KAAK;AAAA,MACxB;AAAA,IACD;AAAA,EACD;AACA,WAAS,UAA4B,QAAW,QAAwB;AAEvE,WAAO,IAAI,SAAS,QAAQ,MAAM;AAAA,EACnC;AAEA,WAAS,eAAe,OAAiB;AACxC,QAAI,CAAC,MAAM,OAAO;AAEjB,YAAM,QAAQ,oBAAI,IAAI;AACtB,YAAM,MAAM,QAAQ,WAAS;AAC5B,YAAI,YAAY,KAAK,GAAG;AACvB,gBAAM,QAAQ,YAAY,OAAO,KAAK;AACtC,gBAAM,QAAQ,IAAI,OAAO,KAAK;AAC9B,gBAAM,MAAO,IAAI,KAAK;AAAA,QACvB,OAAO;AACN,gBAAM,MAAO,IAAI,KAAK;AAAA,QACvB;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AAEA,WAAS,gBAAgB,OAA+C;AACvE,QAAI,MAAM;AAAU,UAAI,GAAG,KAAK,UAAU,OAAO,KAAK,CAAC,CAAC;AAAA,EACzD;AAEA,aAAW,UAAU,EAAC,WAAW,UAAS,CAAC;AAC5C;;;ACrRA,IAAM,QAAQ,IAAIC,OAAM;AAqBjB,IAAM,UAAoC,MAAM;AAMhD,IAAM,qBAA0D,sBAAM,mBAAmB;AAAA,EAC/F;AACD;AAOO,IAAM,gBAAgC,sBAAM,cAAc,KAAK,KAAK;AAOpE,IAAM,0BAA0C,sBAAM,wBAAwB;AAAA,EACpF;AACD;AAOO,IAAM,eAA+B,sBAAM,aAAa,KAAK,KAAK;AAMlE,IAAM,cAA8B,sBAAM,YAAY,KAAK,KAAK;AAUhE,IAAM,cAA8B,sBAAM,YAAY,KAAK,KAAK;AAQhE,SAAS,UAAa,OAAoB;AAChD,SAAO;AACR;AAOO,SAAS,cAAiB,OAAwB;AACxD,SAAO;AACR;", "names": ["immer", "isSet", "current", "Immer", "base", "Immer"]}